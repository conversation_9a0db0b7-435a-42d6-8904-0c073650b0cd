2025-08-16 14:04:44,365 - INFO - 字幕处理完成：生成了文本文件 F:\github\aicut_auto\cutbefore.txt 和 JSON文件 F:\github\aicut_auto\cutbefore.json
2025-08-16 14:04:44,366 - INFO - 文本文件合并完成：F:\github\aicut_auto\短剧指令_新.txt
2025-08-16 14:04:44,367 - INFO - 文本文件合并完成：F:\github\aicut_auto\短剧指令_长.txt
2025-08-16 14:04:44,406 - INFO - 程序开始时GPU状态: NVIDIA GeForce RTX 4070 Ti, 显存 4399MB/12282MB, 温度 29°C, 利用率 2%, 功耗 42.94W
2025-08-16 14:04:44,430 - INFO - NVENC编码器状态: 未知
2025-08-16 14:04:45,443 - INFO - 程序开始时系统状态: 内存使用 50.1%, CPU使用 4.6%, 磁盘可用 253.4GB
2025-08-16 14:04:45,452 - WARNING - 无法删除 F:\github\aicut_auto\ai-video-splitter\splitter_log.txt: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'F:\\github\\aicut_auto\\ai-video-splitter\\splitter_log.txt'
2025-08-16 14:04:45,452 - INFO - 输出目录清空完成: F:\github\aicut_auto\ai-video-splitter
2025-08-16 14:04:45,452 - INFO - 输出目录已存在: F:\github\aicut_auto\ai-video-splitter
2025-08-16 14:04:45,452 - INFO - 开始处理视频: F:\github\aicut_auto\原视频合并\cutbefore.mp4
2025-08-16 14:04:45,453 - INFO - 成功解析字幕文件，共20条字幕
2025-08-16 14:04:47,348 - INFO - Fingerprint not found. Saved model loading will continue.
2025-08-16 14:04:47,348 - INFO - path_and_singleprint metric could not be logged. Saved model loading will continue.
2025-08-16 14:04:53,234 - INFO - 成功检测到36个场景
2025-08-16 14:04:53,706 - INFO - 处理开始时GPU状态: 显存 4351MB/12282MB, 温度 29°C
2025-08-16 14:04:53,706 - INFO - 高性能FFmpeg批量分割开始: 36个场景
2025-08-16 14:04:53,707 - INFO - 获取NVENC会话，当前活跃会话: 1/4
2025-08-16 14:04:53,708 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:04:54,183 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:04:54,183 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:04:54,203 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:04:54,203 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:04:54,648 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:04:54,648 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:04:54,658 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:04:54,659 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:04:55,144 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:04:55,144 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:04:55,370 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:04:55,370 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:04:55,601 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:04:55,601 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:04:55,821 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:04:55,821 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:04:56,063 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:04:56,063 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:04:56,283 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:04:56,283 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:04:56,500 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:04:56,500 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:04:56,720 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:04:56,720 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:04:56,888 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:04:56,888 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:04:57,207 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:04:57,207 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:04:57,274 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:04:57,274 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:04:57,709 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:04:57,709 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:04:57,741 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:04:57,741 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:04:58,181 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:04:58,181 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:04:58,200 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:04:58,200 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:04:58,652 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:04:58,652 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:04:58,689 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:04:58,689 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:04:59,117 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:04:59,117 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:04:59,173 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:04:59,174 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:04:59,516 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:04:59,517 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:04:59,626 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:04:59,626 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:05:00,001 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:05:00,001 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:05:00,061 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:05:00,061 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:05:00,398 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:05:00,398 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:05:00,467 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:05:00,467 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:05:00,839 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:05:00,839 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:05:00,932 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:05:00,932 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:05:01,315 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:05:01,315 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:05:01,355 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:05:01,797 - INFO - 释放NVENC会话，当前活跃会话: 0/4
2025-08-16 14:05:01,797 - INFO - 获取NVENC会话，当前活跃会话: 1/4
2025-08-16 14:05:02,208 - INFO - 释放NVENC会话，当前活跃会话: 0/4
2025-08-16 14:05:02,208 - INFO - 获取NVENC会话，当前活跃会话: 1/4
2025-08-16 14:05:02,589 - INFO - 释放NVENC会话，当前活跃会话: 0/4
2025-08-16 14:05:02,589 - INFO - 高性能FFmpeg批量分割成功，耗时: 8.88秒
2025-08-16 14:05:02,589 - INFO - 高性能批量分割统计: 成功36/36, 失败0, 平均0.247秒/场景, 速度4.1场景/秒
2025-08-16 14:05:02,637 - INFO - 处理完成后GPU状态: 显存 4351MB/12282MB, 温度 29°C
2025-08-16 14:05:02,637 - INFO - GPU处理统计: 成功 36, 失败 0, 成功率 100.0%
2025-08-16 14:05:03,602 - INFO - 关键帧提取完成: 共36张图片
2025-08-16 14:05:03,605 - INFO - 报告已保存到 F:\github\aicut_auto\ai-video-splitter\scenes_report.json
2025-08-16 14:05:03,605 - INFO - 总执行时间: 0分 19.44秒
2025-08-16 14:35:04,912 - INFO - 字幕处理完成：生成了文本文件 F:\github\aicut_auto\cutbefore.txt 和 JSON文件 F:\github\aicut_auto\cutbefore.json
2025-08-16 14:35:04,913 - INFO - 文本文件合并完成：F:\github\aicut_auto\短剧指令_新.txt
2025-08-16 14:35:04,913 - INFO - 文本文件合并完成：F:\github\aicut_auto\短剧指令_长.txt
2025-08-16 14:35:04,969 - INFO - 程序开始时GPU状态: NVIDIA GeForce RTX 4070 Ti, 显存 4412MB/12282MB, 温度 29°C, 利用率 2%, 功耗 43.80W
2025-08-16 14:35:04,997 - INFO - NVENC编码器状态: 未知
2025-08-16 14:35:06,010 - INFO - 程序开始时系统状态: 内存使用 47.0%, CPU使用 5.0%, 磁盘可用 253.4GB
2025-08-16 14:35:06,026 - WARNING - 无法删除 F:\github\aicut_auto\ai-video-splitter\splitter_log.txt: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'F:\\github\\aicut_auto\\ai-video-splitter\\splitter_log.txt'
2025-08-16 14:35:06,026 - INFO - 输出目录清空完成: F:\github\aicut_auto\ai-video-splitter
2025-08-16 14:35:06,026 - INFO - 输出目录已存在: F:\github\aicut_auto\ai-video-splitter
2025-08-16 14:35:06,026 - INFO - 开始处理视频: F:\github\aicut_auto\原视频合并\cutbefore.mp4
2025-08-16 14:35:06,026 - INFO - 成功解析字幕文件，共20条字幕
2025-08-16 14:35:08,220 - INFO - Fingerprint not found. Saved model loading will continue.
2025-08-16 14:35:08,220 - INFO - path_and_singleprint metric could not be logged. Saved model loading will continue.
2025-08-16 14:35:13,718 - ERROR - 处理视频时出错: Could not find matching concrete function to call loaded from the SavedModel. Got:
  Positional arguments (2 total):
    * <tf.Tensor 'inputs:0' shape=(1, 100, 48, 27, 3) dtype=float32>
    * False
  Keyword arguments: {}

 Expected these arguments to match one of the following 4 option(s):

Option 1:
  Positional arguments (2 total):
    * TensorSpec(shape=(None, None, 27, 48, 3), dtype=tf.float32, name='input_1')
    * False
  Keyword arguments: {}

Option 2:
  Positional arguments (2 total):
    * TensorSpec(shape=(None, None, 27, 48, 3), dtype=tf.float32, name='inputs')
    * True
  Keyword arguments: {}

Option 3:
  Positional arguments (2 total):
    * TensorSpec(shape=(None, None, 27, 48, 3), dtype=tf.float32, name='inputs')
    * False
  Keyword arguments: {}

Option 4:
  Positional arguments (2 total):
    * TensorSpec(shape=(None, None, 27, 48, 3), dtype=tf.float32, name='input_1')
    * True
  Keyword arguments: {}
Traceback (most recent call last):
  File "F:\github\aicut_auto\2-2-2_ai_video_splitter(全视频场景分割).py", line 2008, in process_video
    video_frames, single_frame_predictions, all_frame_predictions = predict_video_with_resolution(
                                                                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\github\aicut_auto\2-2-2_ai_video_splitter(全视频场景分割).py", line 3084, in predict_video_with_resolution
    return predict_video_cpu_mode(model, video_fn, resolution)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\github\aicut_auto\2-2-2_ai_video_splitter(全视频场景分割).py", line 3303, in predict_video_cpu_mode
    return video, *model.predict_frames(video)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\github\aicut_auto\2-2-2_ai_video_splitter(全视频场景分割).py", line 1978, in predict_frames_with_progress
    single_frame_pred, all_frames_pred = model.predict_raw(inp)
                                         ^^^^^^^^^^^^^^^^^^^^^^
  File "F:\github\aicut_auto\视频镜头智能分割-代码\transnetv2.py", line 36, in predict_raw
    logits, dict_ = self._model(frames)
                    ^^^^^^^^^^^^^^^^^^^
  File "F:\github\aicut_auto\.venv\Lib\site-packages\tensorflow\python\saved_model\load.py", line 817, in _call_attribute
    return instance.__call__(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\github\aicut_auto\.venv\Lib\site-packages\tensorflow\python\util\traceback_utils.py", line 153, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "F:\github\aicut_auto\.venv\Lib\site-packages\tensorflow\python\saved_model\function_deserialization.py", line 335, in restored_function_body
    raise ValueError(
ValueError: Could not find matching concrete function to call loaded from the SavedModel. Got:
  Positional arguments (2 total):
    * <tf.Tensor 'inputs:0' shape=(1, 100, 48, 27, 3) dtype=float32>
    * False
  Keyword arguments: {}

 Expected these arguments to match one of the following 4 option(s):

Option 1:
  Positional arguments (2 total):
    * TensorSpec(shape=(None, None, 27, 48, 3), dtype=tf.float32, name='input_1')
    * False
  Keyword arguments: {}

Option 2:
  Positional arguments (2 total):
    * TensorSpec(shape=(None, None, 27, 48, 3), dtype=tf.float32, name='inputs')
    * True
  Keyword arguments: {}

Option 3:
  Positional arguments (2 total):
    * TensorSpec(shape=(None, None, 27, 48, 3), dtype=tf.float32, name='inputs')
    * False
  Keyword arguments: {}

Option 4:
  Positional arguments (2 total):
    * TensorSpec(shape=(None, None, 27, 48, 3), dtype=tf.float32, name='input_1')
    * True
  Keyword arguments: {}
2025-08-16 14:36:28,479 - INFO - 字幕处理完成：生成了文本文件 F:\github\aicut_auto\cutbefore.txt 和 JSON文件 F:\github\aicut_auto\cutbefore.json
2025-08-16 14:36:28,480 - INFO - 文本文件合并完成：F:\github\aicut_auto\短剧指令_新.txt
2025-08-16 14:36:28,480 - INFO - 文本文件合并完成：F:\github\aicut_auto\短剧指令_长.txt
2025-08-16 14:36:28,520 - INFO - 程序开始时GPU状态: NVIDIA GeForce RTX 4070 Ti, 显存 4411MB/12282MB, 温度 29°C, 利用率 4%, 功耗 29.18W
2025-08-16 14:36:28,546 - INFO - NVENC编码器状态: 未知
2025-08-16 14:36:29,559 - INFO - 程序开始时系统状态: 内存使用 47.1%, CPU使用 6.4%, 磁盘可用 253.4GB
2025-08-16 14:36:29,560 - WARNING - 无法删除 F:\github\aicut_auto\ai-video-splitter\splitter_log.txt: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'F:\\github\\aicut_auto\\ai-video-splitter\\splitter_log.txt'
2025-08-16 14:36:29,560 - INFO - 输出目录清空完成: F:\github\aicut_auto\ai-video-splitter
2025-08-16 14:36:29,560 - INFO - 输出目录已存在: F:\github\aicut_auto\ai-video-splitter
2025-08-16 14:36:29,560 - INFO - 开始处理视频: F:\github\aicut_auto\原视频合并\cutbefore.mp4
2025-08-16 14:36:29,560 - INFO - 成功解析字幕文件，共20条字幕
2025-08-16 14:36:31,398 - INFO - Fingerprint not found. Saved model loading will continue.
2025-08-16 14:36:31,398 - INFO - path_and_singleprint metric could not be logged. Saved model loading will continue.
2025-08-16 14:36:37,317 - INFO - 成功检测到36个场景
2025-08-16 14:36:37,986 - INFO - 处理开始时GPU状态: 显存 4392MB/12282MB, 温度 29°C
2025-08-16 14:36:37,986 - INFO - 高性能FFmpeg批量分割开始: 36个场景
2025-08-16 14:36:37,987 - INFO - 获取NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:37,987 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:36:38,526 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:38,526 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:36:38,581 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:38,581 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:36:38,951 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:38,951 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:36:39,076 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:39,076 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:36:39,369 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:39,369 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:36:39,664 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:39,664 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:36:39,838 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:39,838 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:36:40,143 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:40,143 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:36:40,350 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:40,350 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:36:40,584 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:40,584 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:36:40,776 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:40,776 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:36:41,004 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:41,004 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:36:41,162 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:41,162 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:36:41,489 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:41,489 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:36:41,578 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:41,578 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:36:41,973 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:41,973 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:36:42,057 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:42,057 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:36:42,439 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:42,439 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:36:42,529 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:42,529 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:36:42,890 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:42,890 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:36:42,955 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:42,955 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:36:43,349 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:43,349 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:36:43,404 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:43,404 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:36:43,783 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:43,783 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:36:43,837 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:43,837 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:36:44,202 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:44,202 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:36:44,343 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:44,343 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:36:44,598 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:44,598 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:36:44,698 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:44,698 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:36:45,076 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:45,076 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:36:45,138 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:45,138 - INFO - 获取NVENC会话，当前活跃会话: 2/4
2025-08-16 14:36:45,492 - INFO - 释放NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:45,596 - INFO - 释放NVENC会话，当前活跃会话: 0/4
2025-08-16 14:36:45,596 - INFO - 获取NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:46,063 - INFO - 释放NVENC会话，当前活跃会话: 0/4
2025-08-16 14:36:46,063 - INFO - 获取NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:46,442 - INFO - 释放NVENC会话，当前活跃会话: 0/4
2025-08-16 14:36:46,442 - INFO - 获取NVENC会话，当前活跃会话: 1/4
2025-08-16 14:36:46,805 - INFO - 释放NVENC会话，当前活跃会话: 0/4
2025-08-16 14:36:46,806 - INFO - 高性能FFmpeg批量分割成功，耗时: 8.82秒
2025-08-16 14:36:46,806 - INFO - 高性能批量分割统计: 成功36/36, 失败0, 平均0.245秒/场景, 速度4.1场景/秒
2025-08-16 14:36:46,843 - INFO - 处理完成后GPU状态: 显存 4392MB/12282MB, 温度 29°C
2025-08-16 14:36:46,843 - INFO - GPU处理统计: 成功 36, 失败 0, 成功率 100.0%
2025-08-16 14:36:48,196 - INFO - 关键帧提取完成: 共36张图片
2025-08-16 14:36:48,198 - INFO - 报告已保存到 F:\github\aicut_auto\ai-video-splitter\scenes_report.json
2025-08-16 14:36:48,198 - INFO - 总执行时间: 0分 19.92秒
