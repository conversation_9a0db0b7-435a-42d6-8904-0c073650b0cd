import os
import json
import datetime
import logging
import sys
import time  # 添加时间模块，用于计算执行时间
import shutil
from typing import List, Dict, Tuple, Any
import multiprocessing
from concurrent.futures import ThreadPoolExecutor

from moviepy.editor import VideoFileClip
import numpy as np
import math

# 尝试导入GPU相关库
try:
    import tensorflow as tf
    # 检查GPU是否可用
    gpus = tf.config.experimental.list_physical_devices('GPU')
    if gpus:
        # 打印可用的GPU设备
        print(f"找到 {len(gpus)} 个GPU设备:")
        for gpu in gpus:
            print(f" - {gpu.name}")
        
        # 配置TensorFlow使用第一个GPU
        try:
            tf.config.experimental.set_memory_growth(gpus[0], True)
            print(f"已启用GPU加速: {gpus[0].name}")
            HAS_GPU = True
        except RuntimeError as e:
            print(f"GPU配置错误: {e}")
            HAS_GPU = False
    else:
        print("未检测到GPU设备，将使用CPU模式")
        HAS_GPU = False
except ImportError:
    print("未安装TensorFlow或无法导入GPU支持，将使用CPU模式")
    HAS_GPU = False

# 尝试导入FFmpeg Python绑定，用于GPU加速视频处理
try:
    import ffmpeg
    HAS_FFMPEG = True
    print("成功加载FFmpeg-python，可用于GPU加速")
except ImportError:
    HAS_FFMPEG = False
    print("未安装FFmpeg-python，部分GPU加速功能将不可用")

# 添加视频镜头智能分割-代码目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), "视频镜头智能分割-代码"))
from transnetv2 import TransNetV2

# 导入字幕处理函数
from process_subtitle import process_srt

# 全局参数设置
# 用于场景检测的分辨率，可调整为更低以提高速度或更高以提高准确性
DETECTION_RESOLUTION = (27, 48)  # 默认与原TransNetV2相同
# 并行处理的线程数，针对i7-13700K(16核24线程)优化
NUM_WORKERS = min(multiprocessing.cpu_count(), 24)  # 使用全部24线程
# GPU相关设置
USE_GPU = HAS_GPU  # 是否使用GPU加速
# 新增：视频帧提取方式选择
USE_CPU_EXTRACT = False  # True=使用CPU提取帧(MoviePy), False=使用GPU解码+CPU缩放混合方案
# 批处理大小设置 - 针对RTX 4070Ti 12GB显存优化
FRAME_BATCH_SIZE = 12000  # 增大批处理大小，充分利用12GB显存
SCENE_BATCH_SIZE = 4000   # 场景分析批量大小，适配大内存
# 添加并行帧提取的设置 - 针对i7-13700K优化
FRAME_EXTRACT_WORKERS = min(8, multiprocessing.cpu_count())  # 帧提取并行线程数，平衡CPU和GPU负载
# GPU解码优化配置 - 针对RTX 4070Ti优化
GPU_BATCH_SIZE = 20000      # 大幅增加GPU批处理大小
USE_GPU_DECODE = True       # 默认启用GPU解码
CUDA_DEVICE = 0             # 使用第一个GPU
# 新增：GPU解码性能优化参数 - 基于正确的性能测试结果优化
GPU_READ_BUFFER_SIZE = 50   # GPU读取缓冲区大小（帧数）- 测试显示50帧最优
GPU_PROCESS_BATCH_SIZE = 100  # GPU批处理大小（帧数）- 测试显示100帧最优  
GPU_IO_BUFFER_SIZE = 1048576  # GPU IO缓冲区大小（字节）- 测试显示1MB最优
# 视频编码设置 - 针对高端配置优化
VIDEO_ENCODE_PRESET = "p1"  # 使用p2预设，平衡速度和质量
VIDEO_BITRATE = "8M"       # 提高码率到10M，充分利用处理能力
# GPU滤镜链兼容性缓存
GPU_FILTER_CONFIG_CACHE = None  # 缓存兼容的GPU配置
# 新增：场景处理模式选择
USE_BATCH_SEGMENT = True        # 是否使用FFmpeg批量分割模式（默认启用，性能更高）
# 新增：高性能并行配置 - 进一步优化，解决NVENC编码器限制
HIGH_PERFORMANCE_MODE = True    # 是否启用高性能模式
GPU_MAX_CONCURRENT = 6          # GPU模式最大并发数（进一步减少，避免NVENC编码器冲突）
CPU_MAX_CONCURRENT = 20          # CPU模式最大并发数（适度增加）
BATCH_PROCESSING_SIZE = 20      # 批处理组大小（减少组内复杂度）
# 新增：NVENC编码器管理配置
NVENC_MAX_SESSIONS = 4          # NVENC最大同时编码会话数（硬件限制通常为3-4个）
NVENC_RETRY_ATTEMPTS = 2        # NVENC失败重试次数
NVENC_RETRY_DELAY = 0.5        # NVENC重试延迟（秒）
# 新增：关键帧保存功能配置
SAVE_KEYFRAMES = True           # 主开关：是否保存场景关键帧（True=保存，False=不保存）
KEYFRAMES_OUTPUT_DIR = "F:/github/aicut_auto/ai-video-splitter/Keyframes"  # 关键帧输出目录
KEYFRAMES_FORMAT = "PNG"        # 图片格式：PNG（高质量）或JPG（小文件）
KEYFRAMES_QUALITY = 95          # JPG质量（1-100，PNG忽略此参数）
KEYFRAMES_RESOLUTION = "original"  # 关键帧分辨率："original"=原始分辨率，或(width,height)元组，如 (1920, 1080)、(1280, 720)
KEYFRAMES_KEEP_ASPECT_RATIO = True  # 保持宽高比
KEYFRAMES_SAVE_START_FRAME = True  # 是否保存场景开始帧
KEYFRAMES_SAVE_END_FRAME = True    # 是否保存场景结束帧
KEYFRAMES_ADD_TIMESTAMP = True     # 文件名是否包含时间戳
KEYFRAMES_ADD_TO_REPORT = True     # 是否在JSON报告中包含关键帧信息
KEYFRAMES_MAX_WORKERS = 16          # 关键帧提取最大线程数（可手动调节：1-16，推荐4-8）

# 配置日志
logging.basicConfig(
    filename='F:\\github\\aicut_auto\\ai-video-splitter\\splitter_log.txt',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# 检查NVIDIA GPU编码支持
def check_nvenc_support():
    """检查系统是否支持NVIDIA硬件编码"""
    if not HAS_FFMPEG:
        return False
    
    try:
        # 尝试直接创建一个简单的测试命令来检查NVENC支持
        print("测试NVIDIA硬件编码支持...")
        try:
            # 创建一个1秒的测试视频来测试GPU编码
            test_input = ffmpeg.input('color=c=black:s=320x240:d=1', f='lavfi')
            test_output = os.path.join(os.getcwd(), 'test_nvenc.mp4')
            
            # 尝试使用NVENC编码
            (
                test_input
                .output(test_output, vcodec='h264_nvenc')
                .global_args('-y')
                .global_args('-loglevel', 'error')
                .run(capture_stdout=True, capture_stderr=True)
            )
            
            # 如果成功，删除测试文件
            if os.path.exists(test_output):
                os.remove(test_output)
                print("✓ NVIDIA硬件编码测试成功")
                return True
                
        except (ffmpeg.Error, Exception) as e:
            print(f"NVIDIA硬件编码测试失败: {str(e)}")
            
            # 如果测试命令失败，尝试使用更简单的方法检查
            import subprocess
            
            # 检查编码器支持
            result = subprocess.run(
                ['ffmpeg', '-encoders'], 
                capture_output=True, 
                text=True
            )
            
            # 检查输出中是否包含nvenc
            output = result.stdout
            if 'h264_nvenc' in output:
                print("检测到支持的NVIDIA H.264 硬件编码器（通过列表）")
                return True
            elif any(nvenc in output for nvenc in ['nvenc', 'hevc_nvenc']):
                print("检测到其他NVIDIA硬件编码器支持（通过列表）")
                return True
                
        print("未检测到NVIDIA硬件编码器支持")
        return False
    except Exception as e:
        print(f"检查NVIDIA编码器时出错: {e}")
        return False

# 检查NVENC支持
HAS_NVENC = check_nvenc_support() if HAS_FFMPEG else False

def detect_gpu_decode_capabilities():
    """检测GPU解码能力"""
    gpu_info = {
        'has_nvdec': False,
        'supported_codecs': [],
        'gpu_memory': 0
    }
    
    if not HAS_FFMPEG:
        return gpu_info
    
    try:
        import subprocess
        # 检测NVDEC支持
        result = subprocess.run(['ffmpeg', '-hwaccels'], 
                              capture_output=True, text=True)
        if 'cuda' in result.stdout:
            gpu_info['has_nvdec'] = True
            print("✓ 检测到CUDA硬件加速支持")
            
        # 检测支持的编解码器
        result = subprocess.run(['ffmpeg', '-decoders'], 
                              capture_output=True, text=True)
        if 'h264_cuvid' in result.stdout:
            gpu_info['supported_codecs'].append('h264_cuvid')
        if 'hevc_cuvid' in result.stdout:
            gpu_info['supported_codecs'].append('hevc_cuvid')
        if 'av1_cuvid' in result.stdout:
            gpu_info['supported_codecs'].append('av1_cuvid')
            
        if gpu_info['supported_codecs']:
            print(f"✓ 支持的GPU解码器: {', '.join(gpu_info['supported_codecs'])}")
            
        # 检测GPU内存（通过nvidia-smi）
        try:
            result = subprocess.run(['nvidia-smi', '--query-gpu=memory.total', 
                                   '--format=csv,noheader,nounits'], 
                                  capture_output=True, text=True)
            gpu_info['gpu_memory'] = int(result.stdout.strip())
            print(f"✓ GPU显存: {gpu_info['gpu_memory']}MB")
        except:
            gpu_info['gpu_memory'] = 12288  # 默认12GB
            print("⚠ 无法检测GPU显存，使用默认值12GB")
            
    except Exception as e:
        print(f"GPU解码能力检测失败: {e}")
        
    return gpu_info

# 检测GPU解码支持
GPU_DECODE_INFO = detect_gpu_decode_capabilities() if HAS_FFMPEG else {'has_nvdec': False, 'supported_codecs': [], 'gpu_memory': 0}

def format_timestamp(seconds: float) -> str:
    """将秒数转换为'小时:分钟:秒.毫秒'格式"""
    hours, remainder = divmod(seconds, 3600)
    minutes, seconds = divmod(remainder, 60)
    milliseconds = int((seconds - int(seconds)) * 1000)
    return f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d},{milliseconds:03d}"


def format_timestamp_for_filename(seconds: float) -> str:
    """将秒数转换为适合文件名的格式：'00h05m12s150ms'"""
    hours, remainder = divmod(seconds, 3600)
    minutes, seconds = divmod(remainder, 60)
    milliseconds = int((seconds - int(seconds)) * 1000)
    return f"{int(hours):02d}h{int(minutes):02d}m{int(seconds):02d}s{milliseconds:03d}ms"


def calculate_target_resolution(video_path, target_resolution, keep_aspect_ratio=True):
    """
    计算目标分辨率，支持原始分辨率和自定义分辨率

    Args:
        video_path: 视频文件路径
        target_resolution: "original" 或 (width, height) 元组
        keep_aspect_ratio: 是否保持宽高比

    Returns:
        (width, height): 计算后的目标分辨率
    """
    from moviepy.editor import VideoFileClip

    # 获取视频原始分辨率
    temp_clip = VideoFileClip(video_path)
    original_width, original_height = temp_clip.size
    temp_clip.close()

    if target_resolution == "original":
        return (original_width, original_height)

    if isinstance(target_resolution, (tuple, list)) and len(target_resolution) == 2:
        target_width, target_height = target_resolution

        if not keep_aspect_ratio:
            return (target_width, target_height)

        # 保持宽高比计算
        original_ratio = original_width / original_height
        target_ratio = target_width / target_height

        if original_ratio > target_ratio:
            # 原始视频更宽，以宽度为准
            final_width = target_width
            final_height = int(target_width / original_ratio)
        else:
            # 原始视频更高，以高度为准
            final_height = target_height
            final_width = int(target_height * original_ratio)

        return (final_width, final_height)

    # 默认返回原始分辨率
    return (original_width, original_height)


def save_high_quality_keyframes_ffmpeg(video_path, scenes, fps, output_dir,
                                      image_format="PNG", quality=95,
                                      target_resolution="original", keep_aspect_ratio=True):
    """
    使用FFmpeg批量提取并保存高质量关键帧（优化版本）

    Args:
        video_path: 视频文件路径
        scenes: 场景边界列表，格式为[(start_frame, end_frame), ...]
        fps: 视频帧率
        output_dir: 输出目录路径
        image_format: 图片格式（PNG/JPG）
        quality: JPG质量（1-100，PNG忽略此参数）
        target_resolution: "original" 或 (width, height) 元组
        keep_aspect_ratio: 是否保持宽高比

    Returns:
        keyframes_info: 关键帧信息字典，用于JSON报告
    """
    import os
    import subprocess
    import tempfile
    import shutil
    import time
    from concurrent.futures import ThreadPoolExecutor, as_completed
    from PIL import Image

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 计算目标分辨率
    final_resolution = calculate_target_resolution(video_path, target_resolution, keep_aspect_ratio)

    print(f"🚀 智能关键帧提取: {len(scenes)}个场景")
    print(f"🎯 目标分辨率: {final_resolution[0]}x{final_resolution[1]}")

    # 收集所有需要提取的帧信息
    keyframes_to_extract = []
    keyframes_info = {
        "total_keyframes": 0,
        "keyframes_directory": os.path.basename(output_dir),
        "resolution": f"{final_resolution[0]}x{final_resolution[1]}",
        "keyframes": []
    }

    for scene_idx, (start_frame, end_frame) in enumerate(scenes):
        scene_info = {
            "scene_id": scene_idx + 1,
            "start_frame": None,
            "end_frame": None
        }

        # 收集开始帧信息
        if KEYFRAMES_SAVE_START_FRAME:
            start_timestamp = start_frame / fps
            if KEYFRAMES_ADD_TIMESTAMP:
                start_filename = f"keyframe_scene{scene_idx+1:03d}_start_{format_timestamp_for_filename(start_timestamp)}_frame{start_frame:06d}.{image_format.lower()}"
            else:
                start_filename = f"keyframe_scene{scene_idx+1:03d}_start_frame{start_frame:06d}.{image_format.lower()}"

            keyframes_to_extract.append({
                "frame_number": start_frame,
                "timestamp": start_timestamp,
                "final_filename": start_filename,
                "scene_idx": scene_idx,
                "frame_type": "start"
            })

            scene_info["start_frame"] = {
                "frame_number": int(start_frame),
                "timestamp": format_timestamp(start_timestamp),
                "filename": start_filename,
                "resolution": f"{final_resolution[0]}x{final_resolution[1]}"
            }

        # 收集结束帧信息
        if KEYFRAMES_SAVE_END_FRAME and end_frame != start_frame:
            end_timestamp = end_frame / fps
            if KEYFRAMES_ADD_TIMESTAMP:
                end_filename = f"keyframe_scene{scene_idx+1:03d}_end_{format_timestamp_for_filename(end_timestamp)}_frame{end_frame:06d}.{image_format.lower()}"
            else:
                end_filename = f"keyframe_scene{scene_idx+1:03d}_end_frame{end_frame:06d}.{image_format.lower()}"

            keyframes_to_extract.append({
                "frame_number": end_frame,
                "timestamp": end_timestamp,
                "final_filename": end_filename,
                "scene_idx": scene_idx,
                "frame_type": "end"
            })

            scene_info["end_frame"] = {
                "frame_number": int(end_frame),
                "timestamp": format_timestamp(end_timestamp),
                "filename": end_filename,
                "resolution": f"{final_resolution[0]}x{final_resolution[1]}"
            }

        # 只有当至少有一帧时才添加到结果中
        if scene_info["start_frame"] or scene_info["end_frame"]:
            keyframes_info["keyframes"].append(scene_info)

    if not keyframes_to_extract:
        print("⚠️ 没有需要提取的关键帧")
        return keyframes_info

    total_keyframes = len(keyframes_to_extract)
    print(f"📊 总计需要提取 {total_keyframes} 张关键帧")

    # 智能选择处理方法
    if total_keyframes < 50:
        print(f"🎯 关键帧数量较少({total_keyframes}张)，使用MoviePy方法以获得最佳性能")
        return _fallback_to_original_method(
            video_path, scenes, fps, output_dir,
            image_format, quality, target_resolution, keep_aspect_ratio
        )
    else:
        print(f"🚀 关键帧数量较多({total_keyframes}张)，使用FFmpeg批量提取")

        # 创建临时目录
        with tempfile.TemporaryDirectory(prefix="keyframes_") as temp_dir:
            try:
                # 使用FFmpeg批量提取关键帧
                success = _extract_keyframes_with_ffmpeg(
                    video_path, keyframes_to_extract, temp_dir,
                    final_resolution, image_format, quality
                )

                if not success:
                    print("❌ FFmpeg提取失败，回退到MoviePy方法")
                    return _fallback_to_original_method(
                        video_path, scenes, fps, output_dir,
                        image_format, quality, target_resolution, keep_aspect_ratio
                    )

                # 并行处理：重命名和移动文件到最终位置
                success_count = _process_extracted_keyframes_parallel(
                    keyframes_to_extract, temp_dir, output_dir
                )

                keyframes_info["total_keyframes"] = success_count
                print(f"\n✅ FFmpeg批量提取完成: 共保存{success_count}张关键帧到 {output_dir}")
                print(f"📐 分辨率: {final_resolution[0]}x{final_resolution[1]}")

                return keyframes_info

            except Exception as e:
                print(f"❌ FFmpeg批量提取过程出错: {e}")
                logging.error(f"FFmpeg批量提取过程出错: {e}")
                # 回退到原始方法
                print("🔄 回退到MoviePy方法...")
                return _fallback_to_original_method(
                    video_path, scenes, fps, output_dir,
                    image_format, quality, target_resolution, keep_aspect_ratio
                )


def _extract_keyframes_with_ffmpeg(video_path, keyframes_to_extract, temp_dir,
                                  final_resolution, image_format, quality):
    """
    使用FFmpeg批量提取关键帧

    Args:
        video_path: 视频文件路径
        keyframes_to_extract: 需要提取的帧信息列表
        temp_dir: 临时目录
        final_resolution: 目标分辨率 (width, height)
        image_format: 图片格式
        quality: 图片质量

    Returns:
        bool: 是否成功
    """
    import subprocess
    import os

    try:
        # 简化版本：使用FFmpeg直接按时间戳提取
        success_count = 0

        print(f"🔧 使用FFmpeg逐个提取关键帧...")

        for idx, keyframe_info in enumerate(keyframes_to_extract):
            timestamp = keyframe_info["timestamp"]

            # 构建输出文件名
            if image_format.upper() in ['JPG', 'JPEG']:
                output_file = os.path.join(temp_dir, f"keyframe_{idx+1:06d}.jpg")
                codec_args = ['-q:v', str(100 - quality)]
            else:
                output_file = os.path.join(temp_dir, f"keyframe_{idx+1:06d}.png")
                codec_args = []

            # 构建分辨率参数
            if final_resolution != "original":
                width, height = final_resolution
                scale_args = ['-vf', f'scale={width}:{height}']
            else:
                scale_args = []

            # 构建FFmpeg命令
            ffmpeg_cmd = [
                'ffmpeg',
                '-ss', f'{timestamp:.3f}',  # 跳转到指定时间
                '-i', video_path,
                '-frames:v', '1',  # 只提取一帧
            ] + scale_args + codec_args + [
                '-y',  # 覆盖输出文件
                output_file
            ]

            # 执行FFmpeg命令
            result = subprocess.run(
                ffmpeg_cmd,
                capture_output=True,
                text=True,
                timeout=30  # 30秒超时
            )

            if result.returncode == 0 and os.path.exists(output_file):
                success_count += 1
            else:
                print(f"⚠️ 提取第{idx+1}帧失败: {keyframe_info['final_filename']}")

            # 显示进度
            if (idx + 1) % 5 == 0 or idx == len(keyframes_to_extract) - 1:
                progress = (idx + 1) / len(keyframes_to_extract) * 100
                print(f"\r🔧 FFmpeg提取进度: {progress:.1f}% ({success_count}/{idx + 1})", end="")

        print(f"\n⚡ FFmpeg提取完成: {success_count}/{len(keyframes_to_extract)} 张关键帧")

        return success_count > 0

    except Exception as e:
        print(f"❌ FFmpeg执行异常: {e}")
        return False


def _process_extracted_keyframes_parallel(keyframes_to_extract, temp_dir, output_dir):
    """
    并行处理提取的关键帧：重命名和移动到最终位置

    Args:
        keyframes_to_extract: 需要处理的帧信息列表
        temp_dir: 临时目录
        output_dir: 输出目录

    Returns:
        int: 成功处理的文件数量
    """
    import os
    import shutil
    from concurrent.futures import ThreadPoolExecutor, as_completed

    def process_single_keyframe(keyframe_info, temp_index):
        """处理单个关键帧文件"""
        try:
            # 构建临时文件名（FFmpeg输出的文件名）
            temp_filename = f"keyframe_{temp_index:06d}.jpg" if keyframe_info.get("format", "PNG").upper() in ['JPG', 'JPEG'] else f"keyframe_{temp_index:06d}.png"
            temp_filepath = os.path.join(temp_dir, temp_filename)

            # 最终文件路径
            final_filepath = os.path.join(output_dir, keyframe_info["final_filename"])

            # 检查临时文件是否存在
            if not os.path.exists(temp_filepath):
                print(f"⚠️ 临时文件不存在: {temp_filename}")
                return False

            # 移动文件到最终位置
            shutil.move(temp_filepath, final_filepath)

            return True

        except Exception as e:
            print(f"⚠️ 处理关键帧失败 {keyframe_info.get('final_filename', 'unknown')}: {e}")
            return False

    print(f"🔄 开始并行处理 {len(keyframes_to_extract)} 张关键帧...")

    success_count = 0
    total_count = len(keyframes_to_extract)

    # 使用线程池并行处理
    max_workers = min(4, os.cpu_count() or 1)  # 最多4个线程

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_keyframe = {
            executor.submit(process_single_keyframe, keyframe_info, idx + 1): (idx, keyframe_info)
            for idx, keyframe_info in enumerate(keyframes_to_extract)
        }

        # 处理完成的任务
        for future in as_completed(future_to_keyframe):
            idx, keyframe_info = future_to_keyframe[future]
            try:
                success = future.result()
                if success:
                    success_count += 1

                # 显示进度
                progress = (idx + 1) / total_count * 100
                if (idx + 1) % 20 == 0 or idx == total_count - 1:
                    print(f"\r🔄 处理进度: {progress:.1f}% ({success_count}/{idx + 1})", end="")

            except Exception as e:
                print(f"\n⚠️ 处理任务异常: {e}")

    print(f"\n✅ 并行处理完成: {success_count}/{total_count} 张关键帧成功")
    return success_count


def extract_keyframes_from_scene_videos(scenes_dir, output_dir,
                                       image_format="PNG", quality=95,
                                       target_resolution="original", keep_aspect_ratio=True,
                                       max_workers=None):
    """
    从场景视频文件中多线程批量提取关键帧（新逻辑）

    Args:
        scenes_dir: 场景视频文件目录
        output_dir: 关键帧输出目录
        image_format: 图片格式（PNG/JPG）
        quality: JPG质量（1-100，PNG忽略此参数）
        target_resolution: "original" 或 (width, height) 元组
        keep_aspect_ratio: 是否保持宽高比
        max_workers: 最大线程数（None时使用全局配置KEYFRAMES_MAX_WORKERS）

    Returns:
        keyframes_info: 关键帧信息字典，用于JSON报告
    """
    import os
    import glob
    import subprocess
    import time
    from concurrent.futures import ThreadPoolExecutor, as_completed
    from PIL import Image

    # 使用全局配置的线程数
    if max_workers is None:
        max_workers = KEYFRAMES_MAX_WORKERS

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 查找所有场景视频文件（匹配实际的命名格式：数字.mp4）
    scene_pattern = os.path.join(scenes_dir, "*.mp4")
    all_files = glob.glob(scene_pattern)

    # 过滤出数字命名的场景视频文件，排除其他文件
    scene_files = []
    for file_path in all_files:
        filename = os.path.basename(file_path)
        # 检查是否是纯数字命名的mp4文件（如 1.mp4, 2.mp4, 1000.mp4）
        if filename.replace('.mp4', '').isdigit():
            scene_files.append(file_path)

    # 按数字顺序排序
    scene_files.sort(key=lambda x: int(os.path.basename(x).replace('.mp4', '')))

    if not scene_files:
        print(f"⚠️ 在 {scenes_dir} 中未找到场景视频文件")
        return {
            "total_keyframes": 0,
            "keyframes_directory": os.path.basename(output_dir),
            "resolution": "unknown",
            "keyframes": []
        }

    print(f"📊 发现 {len(scene_files)} 个场景视频文件")
    print(f"🎯 目标分辨率: {target_resolution}")
    print(f"🔧 最大线程数: {max_workers}")

    # 准备关键帧信息
    keyframes_info = {
        "total_keyframes": 0,
        "keyframes_directory": os.path.basename(output_dir),
        "resolution": "unknown",
        "keyframes": []
    }

    # 多线程处理场景视频
    start_time = time.time()
    success_count = 0

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_scene = {
            executor.submit(
                _extract_keyframes_from_single_scene,
                scene_file, output_dir, image_format, quality,
                target_resolution, keep_aspect_ratio
            ): scene_file
            for scene_file in scene_files
        }

        # 处理完成的任务
        for future in as_completed(future_to_scene):
            scene_file = future_to_scene[future]
            try:
                result = future.result()
                if result and result.get("success", False):
                    success_count += result.get("keyframes_count", 0)
                    keyframes_info["keyframes"].append(result.get("scene_info", {}))

                    # 更新分辨率信息（从第一个成功的结果获取）
                    if keyframes_info["resolution"] == "unknown" and result.get("resolution"):
                        keyframes_info["resolution"] = result["resolution"]

                # 显示进度
                completed = len([f for f in future_to_scene if f.done()])
                progress = completed / len(scene_files) * 100
                if completed % 10 == 0 or completed == len(scene_files):
                    print(f"\r🔄 关键帧提取进度: {progress:.1f}% ({completed}/{len(scene_files)})", end="")

            except Exception as e:
                scene_name = os.path.basename(scene_file)
                print(f"\n⚠️ 处理场景 {scene_name} 失败: {e}")
                logging.error(f"处理场景 {scene_name} 失败: {e}")

    elapsed_time = time.time() - start_time
    keyframes_info["total_keyframes"] = success_count

    print(f"\n✅ 多线程关键帧提取完成:")
    print(f"   - 处理时间: {elapsed_time:.2f} 秒")
    print(f"   - 成功提取: {success_count} 张关键帧")
    print(f"   - 处理速度: {success_count / elapsed_time:.1f} 帧/秒")
    print(f"   - 输出目录: {output_dir}")

    return keyframes_info


def _extract_keyframes_from_single_scene(scene_file, output_dir, image_format, quality,
                                        target_resolution, keep_aspect_ratio):
    """
    从单个场景视频文件提取关键帧

    Args:
        scene_file: 场景视频文件路径
        output_dir: 输出目录
        image_format: 图片格式
        quality: 图片质量
        target_resolution: 目标分辨率
        keep_aspect_ratio: 是否保持宽高比

    Returns:
        dict: 处理结果
    """
    import subprocess
    import os
    from PIL import Image

    try:
        # 从文件名提取场景信息（实际格式为数字.mp4，如 1.mp4, 2.mp4）
        scene_name = os.path.basename(scene_file).replace('.mp4', '')

        # 解析场景编号（数字格式）
        if scene_name.isdigit():
            scene_id = int(scene_name)
        else:
            scene_id = 0

        # 构建分辨率参数
        scale_args = []
        if target_resolution != "original":
            if isinstance(target_resolution, (tuple, list)) and len(target_resolution) == 2:
                width, height = target_resolution
                if keep_aspect_ratio:
                    scale_args = ['-vf', f'scale={width}:{height}:force_original_aspect_ratio=decrease,pad={width}:{height}:(ow-iw)/2:(oh-ih)/2']
                else:
                    scale_args = ['-vf', f'scale={width}:{height}']

        # 构建质量参数
        quality_args = []
        if image_format.upper() in ['JPG', 'JPEG']:
            quality_args = ['-q:v', str(100 - quality)]

        # 准备提取任务
        extraction_tasks = []
        success_count = 0
        resolution_info = "unknown"

        # 根据全局配置决定是否提取开始帧
        start_filename = None
        start_filepath = None
        if KEYFRAMES_SAVE_START_FRAME:
            # 构建时间戳（用于文件名）
            if KEYFRAMES_ADD_TIMESTAMP:
                start_filename = f"keyframe_scene{scene_id:03d}_start_00h00m00s000ms.{image_format.lower()}"
            else:
                start_filename = f"keyframe_scene{scene_id:03d}_start.{image_format.lower()}"

            start_filepath = os.path.join(output_dir, start_filename)

            start_cmd = [
                'ffmpeg', '-i', scene_file,
                '-frames:v', '1'
            ] + scale_args + quality_args + [
                '-y', start_filepath
            ]
            extraction_tasks.append(('start', start_cmd, start_filepath))

        # 根据全局配置决定是否提取结束帧
        end_filename = None
        end_filepath = None
        if KEYFRAMES_SAVE_END_FRAME:
            # 构建时间戳（用于文件名）
            if KEYFRAMES_ADD_TIMESTAMP:
                end_filename = f"keyframe_scene{scene_id:03d}_end_99h99m99s999ms.{image_format.lower()}"
            else:
                end_filename = f"keyframe_scene{scene_id:03d}_end.{image_format.lower()}"

            end_filepath = os.path.join(output_dir, end_filename)

            end_cmd = [
                'ffmpeg', '-i', scene_file,
                '-sseof', '-1', '-frames:v', '1'
            ] + scale_args + quality_args + [
                '-y', end_filepath
            ]
            extraction_tasks.append(('end', end_cmd, end_filepath))

        # 如果没有任何提取任务，直接返回
        if not extraction_tasks:
            return {
                "success": False,
                "keyframes_count": 0,
                "resolution": "unknown",
                "scene_info": {
                    "scene_id": scene_id,
                    "scene_name": scene_name,
                    "start_frame": None,
                    "end_frame": None,
                    "resolution": "unknown"
                }
            }

        # 并行执行提取任务
        from concurrent.futures import ThreadPoolExecutor
        with ThreadPoolExecutor(max_workers=min(2, len(extraction_tasks))) as executor:
            futures = []
            for task_type, cmd, filepath in extraction_tasks:
                future = executor.submit(subprocess.run, cmd, capture_output=True, text=True)
                futures.append((task_type, future, filepath))

            # 收集结果
            for task_type, future, filepath in futures:
                result = future.result()
                if result.returncode == 0 and os.path.exists(filepath):
                    success_count += 1

        # 获取分辨率信息（从第一个成功的文件）
        if success_count > 0:
            try:
                test_file = None
                if start_filepath and os.path.exists(start_filepath):
                    test_file = start_filepath
                elif end_filepath and os.path.exists(end_filepath):
                    test_file = end_filepath

                if test_file:
                    img = Image.open(test_file)
                    resolution_info = f"{img.size[0]}x{img.size[1]}"
                    img.close()
            except:
                pass

        # 构建场景信息
        scene_info = {
            "scene_id": scene_id,
            "scene_name": scene_name,
            "resolution": resolution_info
        }

        # 根据配置添加帧信息
        if KEYFRAMES_SAVE_START_FRAME:
            scene_info["start_frame"] = {
                "filename": start_filename if start_filepath and os.path.exists(start_filepath) else None,
                "resolution": resolution_info
            }

        if KEYFRAMES_SAVE_END_FRAME:
            scene_info["end_frame"] = {
                "filename": end_filename if end_filepath and os.path.exists(end_filepath) else None,
                "resolution": resolution_info
            }

        return {
            "success": success_count > 0,
            "keyframes_count": success_count,
            "resolution": resolution_info,
            "scene_info": scene_info
        }

    except Exception as e:
        return {
            "success": False,
            "keyframes_count": 0,
            "error": str(e)
        }


def _fallback_to_original_method(video_path, scenes, fps, output_dir,
                                image_format, quality, target_resolution, keep_aspect_ratio):
    """
    回退到原始的MoviePy方法（当FFmpeg失败时使用）
    """
    print("🔄 回退到原始MoviePy方法...")

    import os
    from PIL import Image
    import numpy as np
    from moviepy.editor import VideoFileClip

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 计算目标分辨率
    final_resolution = calculate_target_resolution(video_path, target_resolution, keep_aspect_ratio)

    keyframes_info = {
        "total_keyframes": 0,
        "keyframes_directory": os.path.basename(output_dir),
        "resolution": f"{final_resolution[0]}x{final_resolution[1]}",
        "keyframes": []
    }

    saved_count = 0

    print(f"📊 使用原始方法保存关键帧: {len(scenes)}个场景")
    print(f"🎯 目标分辨率: {final_resolution[0]}x{final_resolution[1]}")

    # 打开视频文件
    # 注意：MoviePy的target_resolution参数格式是(高度, 宽度)，不是(宽度, 高度)
    moviepy_resolution = (final_resolution[1], final_resolution[0])  # 转换为(高度, 宽度)
    clip = VideoFileClip(video_path, target_resolution=moviepy_resolution)

    try:
        for scene_idx, (start_frame, end_frame) in enumerate(scenes):
            scene_info = {
                "scene_id": scene_idx + 1,
                "start_frame": None,
                "end_frame": None
            }

            # 保存场景开始帧
            if KEYFRAMES_SAVE_START_FRAME:
                start_timestamp = start_frame / fps
                if KEYFRAMES_ADD_TIMESTAMP:
                    start_filename = f"keyframe_scene{scene_idx+1:03d}_start_{format_timestamp_for_filename(start_timestamp)}_frame{start_frame:06d}.{image_format.lower()}"
                else:
                    start_filename = f"keyframe_scene{scene_idx+1:03d}_start_frame{start_frame:06d}.{image_format.lower()}"
                start_filepath = os.path.join(output_dir, start_filename)

                try:
                    # 获取指定时间的帧
                    frame = clip.get_frame(start_timestamp)

                    # 转换为PIL图像
                    pil_image = Image.fromarray(frame.astype(np.uint8), 'RGB')

                    # 保存图像
                    if image_format.upper() == 'JPG' or image_format.upper() == 'JPEG':
                        pil_image.save(start_filepath, 'JPEG', quality=quality)
                    else:
                        pil_image.save(start_filepath, 'PNG')

                    scene_info["start_frame"] = {
                        "frame_number": int(start_frame),
                        "timestamp": format_timestamp(start_timestamp),
                        "filename": start_filename,
                        "resolution": f"{final_resolution[0]}x{final_resolution[1]}"
                    }
                    saved_count += 1

                except Exception as e:
                    print(f"⚠️ 保存场景{scene_idx+1}开始帧失败: {e}")
                    logging.error(f"保存场景{scene_idx+1}开始帧失败: {e}")

            # 保存场景结束帧
            if KEYFRAMES_SAVE_END_FRAME and end_frame != start_frame:
                end_timestamp = end_frame / fps
                if KEYFRAMES_ADD_TIMESTAMP:
                    end_filename = f"keyframe_scene{scene_idx+1:03d}_end_{format_timestamp_for_filename(end_timestamp)}_frame{end_frame:06d}.{image_format.lower()}"
                else:
                    end_filename = f"keyframe_scene{scene_idx+1:03d}_end_frame{end_frame:06d}.{image_format.lower()}"
                end_filepath = os.path.join(output_dir, end_filename)

                try:
                    # 获取指定时间的帧
                    frame = clip.get_frame(end_timestamp)

                    # 转换为PIL图像
                    pil_image = Image.fromarray(frame.astype(np.uint8), 'RGB')

                    # 保存图像
                    if image_format.upper() == 'JPG' or image_format.upper() == 'JPEG':
                        pil_image.save(end_filepath, 'JPEG', quality=quality)
                    else:
                        pil_image.save(end_filepath, 'PNG')

                    scene_info["end_frame"] = {
                        "frame_number": int(end_frame),
                        "timestamp": format_timestamp(end_timestamp),
                        "filename": end_filename,
                        "resolution": f"{final_resolution[0]}x{final_resolution[1]}"
                    }
                    saved_count += 1

                except Exception as e:
                    print(f"⚠️ 保存场景{scene_idx+1}结束帧失败: {e}")
                    logging.error(f"保存场景{scene_idx+1}结束帧失败: {e}")

            # 只有当至少保存了一帧时才添加到结果中
            if scene_info["start_frame"] or scene_info["end_frame"]:
                keyframes_info["keyframes"].append(scene_info)

            # 显示进度
            if (scene_idx + 1) % 10 == 0 or scene_idx == len(scenes) - 1:
                progress = (scene_idx + 1) / len(scenes) * 100
                print(f"\r🖼️ 关键帧保存进度: {progress:.1f}% ({scene_idx + 1}/{len(scenes)}场景)", end="")

    finally:
        clip.close()

    keyframes_info["total_keyframes"] = saved_count
    print(f"\n✅ 原始方法完成: 共保存{saved_count}张图片到 {output_dir}")
    print(f"📐 分辨率: {final_resolution[0]}x{final_resolution[1]}")

    return keyframes_info


def parse_srt_time(time_str: str) -> float:
    """将SRT时间格式转换为秒数"""
    try:
        # 确保时间格式正确
        if time_str.count(':') < 2:
            time_str = '00:' + time_str
        
        # 处理不同的毫秒分隔符
        if ',' in time_str:
            parts = time_str.split(',')
            time_part = parts[0]
            ms_part = parts[1]
        elif '.' in time_str:
            parts = time_str.split('.')
            time_part = parts[0]
            ms_part = parts[1]
        else:
            time_part = time_str
            ms_part = '0'
        
        # 解析时分秒
        hours, minutes, seconds = time_part.split(':')
        
        # 计算总秒数
        total_seconds = (int(hours) * 3600 + 
                        int(minutes) * 60 + 
                        int(seconds) + 
                        int(ms_part) / 1000)
        return total_seconds
    except Exception as e:
        logging.error(f"解析时间戳出错: {time_str}, 错误: {str(e)}")
        return 0.0


def parse_srt_file(srt_path: str) -> List[Dict[str, Any]]:
    """解析SRT字幕文件，返回字幕列表"""
    if not os.path.exists(srt_path):
        logging.error(f"字幕文件不存在: {srt_path}")
        return []
    
    subtitles = []
    try:
        with open(srt_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查SRT格式
        lines = content.strip().split('\n')
        i = 0
        subtitle_number = 1
        
        # 如果是标准格式的SRT文件，尝试正常解析
        if len(lines) >= 3 and lines[1].find(' --> ') > 0 and lines[0].isdigit():
            blocks = content.strip().split('\n\n')
            for block in blocks:
                lines = block.strip().split('\n')
                if len(lines) >= 3:
                    try:
                        subtitle_number = int(lines[0])
                        time_line = lines[1]
                        text = '\n'.join(lines[2:])
                        
                        time_parts = time_line.split(' --> ')
                        start_time = time_parts[0].strip()
                        end_time = time_parts[1].strip()
                        
                        # 确保时间格式正确
                        if start_time.count(':') < 2:
                            start_time = '00:' + start_time
                        if end_time.count(':') < 2:
                            end_time = '00:' + end_time
                        
                        # 确保毫秒部分格式正确
                        if ',' not in start_time:
                            start_time = start_time.replace('.', ',')
                            if ',' not in start_time:
                                start_time += ',000'
                        if ',' not in end_time:
                            end_time = end_time.replace('.', ',')
                            if ',' not in end_time:
                                end_time += ',000'
                                
                        subtitles.append({
                            'number': subtitle_number,
                            'start_time': start_time,
                            'end_time': end_time,
                            'start_seconds': parse_srt_time(start_time),
                            'end_seconds': parse_srt_time(end_time),
                            'text': text
                        })
                    except Exception as e:
                        logging.error(f"解析字幕块时出错: {block}. 错误: {str(e)}")
        # 处理非标准格式：时间戳行 + 文本行 的重复模式
        else:
            while i < len(lines):
                try:
                    # 查找时间戳行
                    if i < len(lines) and ' --> ' in lines[i]:
                        time_line = lines[i]
                        time_parts = time_line.split(' --> ')
                        start_time = time_parts[0].strip()
                        end_time = time_parts[1].strip()
                        
                        # 确保时间格式正确
                        if start_time.count(':') < 2:
                            start_time = '00:' + start_time
                        if end_time.count(':') < 2:
                            end_time = '00:' + end_time
                        
                        # 确保毫秒部分格式正确
                        if ',' not in start_time:
                            start_time = start_time.replace('.', ',')
                            if ',' not in start_time:
                                start_time += ',000'
                        if ',' not in end_time:
                            end_time = end_time.replace('.', ',')
                            if ',' not in end_time:
                                end_time += ',000'
                        
                        # 获取文本
                        text = ""
                        i += 1
                        while i < len(lines) and ' --> ' not in lines[i] and lines[i].strip():
                            text += lines[i] + "\n"
                            i += 1
                        
                        # 跳过空行
                        while i < len(lines) and not lines[i].strip():
                            i += 1
                        
                        if text.strip():
                            subtitles.append({
                                'number': subtitle_number,
                                'start_time': start_time,
                                'end_time': end_time,
                                'start_seconds': parse_srt_time(start_time),
                                'end_seconds': parse_srt_time(end_time),
                                'text': text.strip()
                            })
                            subtitle_number += 1
                    else:
                        i += 1
                except Exception as e:
                    logging.error(f"解析非标准字幕时出错，行: {i}, 错误: {str(e)}")
                    i += 1
        
        if not subtitles:
            logging.error("未能解析任何字幕内容")
        
        return subtitles
    except Exception as e:
        logging.error(f"解析字幕文件时发生异常: {str(e)}")
        return []


def determine_subtitle_position(time: float, subtitles: List[Dict[str, Any]]) -> Tuple[str, Dict[str, Any]]:
    """确定时间点在哪个字幕内或字幕间"""
    for subtitle in subtitles:
        # 如果在字幕时间范围内
        if subtitle['start_seconds'] <= time <= subtitle['end_seconds']:
            return 'inside', subtitle
        
        # 如果是最后一个字幕且在其后
        if subtitle == subtitles[-1] and time > subtitle['end_seconds']:
            return 'after_last', subtitle
    
    # 检查是否在字幕间
    for i in range(len(subtitles) - 1):
        current = subtitles[i]
        next_sub = subtitles[i + 1]
        
        if current['end_seconds'] < time < next_sub['start_seconds']:
            return 'between', {'current': current, 'next': next_sub}
    
    # 如果在第一个字幕之前
    if subtitles and time < subtitles[0]['start_seconds']:
        return 'before_first', subtitles[0]
    
    return 'unknown', None

def determine_scene_position(start_time: float, end_time: float, subtitles: List[Dict[str, Any]]) -> Tuple[str, Dict[str, Any]]:
    """确定场景的位置，考虑到场景可能跨越字幕"""
    start_pos, start_info = determine_subtitle_position(start_time, subtitles)
    end_pos, end_info = determine_subtitle_position(end_time, subtitles)
    
    # 情况1: 场景完全在单个字幕内
    if start_pos == 'inside' and end_pos == 'inside' and start_info['number'] == end_info['number']:
        return 'inside_single', {'subtitle': start_info}
    
    # 情况2: 场景开始或结束与某字幕重叠
    if start_pos == 'inside':
        # 场景开始位于某字幕内，直接归属于该字幕
        return 'overlap_with_subtitle', {'subtitle': start_info, 'position': 'start'}
    
    if end_pos == 'inside':
        # 场景结束位于某字幕内，直接归属于该字幕
        return 'overlap_with_subtitle', {'subtitle': end_info, 'position': 'end'}
    
    # 情况3: 场景完全在字幕间
    if start_pos == 'between' and end_pos == 'between' and start_info['current']['number'] == end_info['current']['number']:
        return 'between', {'current': start_info['current'], 'next': start_info['next']}
    
    # 情况4: 在第一个字幕之前
    if start_pos == 'before_first':
        return 'before_first', {'subtitle': start_info}
    
    # 情况5: 在最后一个字幕之后
    if start_pos == 'after_last':
        return 'after_last', {'subtitle': start_info}
    
    # 情况6: 跨越多个字幕
    # 寻找与场景重叠最多的字幕
    overlapping_subtitles = []
    for subtitle in subtitles:
        subtitle_start = subtitle['start_seconds']
        subtitle_end = subtitle['end_seconds']
        
        # 计算重叠部分
        overlap_start = max(start_time, subtitle_start)
        overlap_end = min(end_time, subtitle_end)
        
        if overlap_end > overlap_start:  # 有重叠
            overlap_duration = overlap_end - overlap_start
            overlapping_subtitles.append({
                'subtitle': subtitle,
                'overlap_duration': overlap_duration
            })
    
    # 如果有重叠的字幕，选择重叠时间最长的
    if overlapping_subtitles:
        overlapping_subtitles.sort(key=lambda x: x['overlap_duration'], reverse=True)
        return 'overlap_with_subtitle', {'subtitle': overlapping_subtitles[0]['subtitle']}
    
    # 如果没有重叠的字幕，按照开始时间判断
    return 'spans_multiple', {'start_info': start_info, 'end_info': end_info}

def generate_scene_filename(scene_index: int, start_time: float, end_time: float, 
                          subtitles: List[Dict[str, Any]], scene_counts: Dict[str, int]) -> str:
    """生成场景文件名
    
    新的命名逻辑:
    - 仅使用场景序号: {场景序号}.mp4
    """
    return f"{scene_index}.mp4"

def get_subtitle_info(start_time: float, end_time: float, subtitles: List[Dict[str, Any]]) -> Dict[str, Any]:
    """获取场景相关的字幕信息
    
    新逻辑：
    - 总是检查所有字幕，查找与场景时间范围有重叠的
    - 对于字幕间场景，保留原有平均分配逻辑
    - 对于第一个字幕前和最后一个字幕后的场景，保留原有分配逻辑
    """
    # 保留场景位置信息作为参考
    scene_pos, scene_info = determine_scene_position(start_time, end_time, subtitles)
    
    subtitle_info = {
        'position': scene_pos,
        'related_subtitles': []
    }
    
    # 查找所有与场景有重叠的字幕
    overlapping_subtitles = []
    for subtitle in subtitles:
        # 检查是否有重叠
        overlap_start = max(start_time, subtitle['start_seconds'])
        overlap_end = min(end_time, subtitle['end_seconds'])
        
        if overlap_end > overlap_start:  # 有重叠
            overlapping_subtitles.append(subtitle)
    
    # 如果有重叠的字幕，直接使用这些字幕
    if overlapping_subtitles:
        for subtitle in overlapping_subtitles:
            subtitle_info['related_subtitles'].append({
                'number': subtitle['number'],
                'start_time': subtitle['start_time'],
                'end_time': subtitle['end_time'],
                'text': subtitle['text']
            })
    # 如果没有重叠的字幕，使用原有逻辑处理特殊情况
    else:
        # 场景在字幕间 - 保留原有分配逻辑
        if scene_pos == 'between':
            # 场景在两个字幕之间，添加前后字幕
            subtitle_info['related_subtitles'].append({
                'number': scene_info['current']['number'],
                'start_time': scene_info['current']['start_time'],
                'end_time': scene_info['current']['end_time'],
                'text': scene_info['current']['text'],
                'allocation': 'previous_subtitle' # 标记为前一字幕
            })
            subtitle_info['related_subtitles'].append({
                'number': scene_info['next']['number'],
                'start_time': scene_info['next']['start_time'],
                'end_time': scene_info['next']['end_time'],
                'text': scene_info['next']['text'],
                'allocation': 'next_subtitle' # 标记为后一字幕
            })
            # 在between_scenes_metadata中添加元数据，用于后续平均分配处理
            between_key = f"between_{scene_info['current']['number']}_{scene_info['next']['number']}"
            subtitle_info['between_scenes_metadata'] = {
                'between_key': between_key,
                'current_subtitle': scene_info['current']['number'],
                'next_subtitle': scene_info['next']['number']
            }
            
        # 处理位于第一个字幕之前的场景
        elif scene_pos == 'before_first':
            # 归属于第一个字幕
            first_subtitle = scene_info['subtitle']
            subtitle_info['related_subtitles'].append({
                'number': first_subtitle['number'],
                'start_time': first_subtitle['start_time'],
                'end_time': first_subtitle['end_time'],
                'text': first_subtitle['text'],
                'allocation': 'first_subtitle' # 标记为第一个字幕
            })
            
        # 处理位于最后一个字幕之后的场景
        elif scene_pos == 'after_last':
            # 归属于最后一个字幕
            last_subtitle = scene_info['subtitle']
            subtitle_info['related_subtitles'].append({
                'number': last_subtitle['number'],
                'start_time': last_subtitle['start_time'],
                'end_time': last_subtitle['end_time'],
                'text': last_subtitle['text'],
                'allocation': 'last_subtitle' # 标记为最后一个字幕
            })
    
    # 按字幕序号排序
    subtitle_info['related_subtitles'].sort(key=lambda x: x['number'])
    
    return subtitle_info


def process_scene_gpu(args) -> Dict[str, Any]:
    """
    使用GPU加速处理单个场景
    """
    i, start_frame, end_frame, fps, video_path, output_dir, subtitles, scene_counts = args
    
    # 计算时间
    start_time_scene = start_frame / fps
    end_time_scene = end_frame / fps
    duration = end_time_scene - start_time_scene
    
    try:
        # 生成文件名 - 现在只使用场景序号
        scene_index = i + 1
        filename = generate_scene_filename(scene_index, start_time_scene, end_time_scene, subtitles, scene_counts)
        output_path = os.path.join(output_dir, filename)
        
        # 获取字幕信息
        subtitle_info = get_subtitle_info(start_time_scene, end_time_scene, subtitles)
        
        # 使用FFmpeg的GPU加速提取场景并编码
        if HAS_FFMPEG and HAS_NVENC:
            temp_output = os.path.join(output_dir, f"temp_{scene_index}.mp4")
            
            try:
                # 使用更简单的GPU加速配置 - 减少可能的错误参数
                input_options = {
                    'ss': str(start_time_scene),
                    't': str(duration),
                    'hwaccel': 'cuda'  # 使用CUDA硬件加速解码
                }
                
                # 使用更基本的NVENC配置参数
                output_options = {
                    'vcodec': 'h264_nvenc',       # NVIDIA硬件编码器
                    'b:v': VIDEO_BITRATE,         # 视频码率
                    'preset': VIDEO_ENCODE_PRESET # NVENC预设
                }
                
                # 创建FFmpeg命令
                ffmpeg_cmd = (
                    ffmpeg
                    .input(video_path, **input_options)
                    .output(temp_output, **output_options)
                    .global_args('-hide_banner')
                    .global_args('-loglevel', 'error')  # 只显示错误
                    .global_args('-y')                  # 自动覆盖输出文件
                )
                
                # 记录开始处理的详细信息
                logging.info(f"开始GPU处理场景 {scene_index}: 时间段 {start_time_scene:.2f}s-{end_time_scene:.2f}s, 时长 {duration:.2f}s")
                
                # 简化日志输出，不显示完整命令
                print(f"正在处理场景 {scene_index}... ", end="", flush=True)
                
                try:
                    # 运行FFmpeg命令，捕获详细输出
                    stdout, stderr = ffmpeg_cmd.run(capture_stdout=True, capture_stderr=True)
                    
                    # 成功后重命名文件
                    if os.path.exists(temp_output):
                        shutil.move(temp_output, output_path)
                        print(f"GPU完成", flush=True)
                        logging.info(f"场景 {scene_index} GPU处理成功")
                    else:
                        raise Exception("GPU编码输出文件未找到")
                        
                except ffmpeg.Error as e:
                    # 详细记录FFmpeg错误信息
                    stderr_output = e.stderr.decode('utf8', errors='replace') if e.stderr else "无stderr输出"
                    stdout_output = e.stdout.decode('utf8', errors='replace') if e.stdout else "无stdout输出"
                    
                    # 记录到日志文件
                    logging.error(f"场景 {scene_index} FFmpeg GPU处理失败:")
                    logging.error(f"  - 命令返回码: {e.returncode}")
                    logging.error(f"  - 时间段: {start_time_scene:.2f}s-{end_time_scene:.2f}s")
                    logging.error(f"  - 时长: {duration:.2f}s")
                    logging.error(f"  - stderr输出: {stderr_output}")
                    logging.error(f"  - stdout输出: {stdout_output}")
                    
                    # 分析具体失败原因
                    failure_reason = "未知原因"
                    if "out of memory" in stderr_output.lower() or "cuda out of memory" in stderr_output.lower():
                        failure_reason = "GPU显存不足"
                    elif "device busy" in stderr_output.lower() or "resource busy" in stderr_output.lower():
                        failure_reason = "GPU设备忙碌/资源竞争"
                    elif "codec not found" in stderr_output.lower() or "encoder not found" in stderr_output.lower():
                        failure_reason = "编码器不可用"
                    elif "invalid argument" in stderr_output.lower():
                        failure_reason = "参数错误"
                    elif "permission denied" in stderr_output.lower():
                        failure_reason = "权限问题"
                    elif "no such file" in stderr_output.lower():
                        failure_reason = "文件访问问题"
                    elif "timeout" in stderr_output.lower():
                        failure_reason = "处理超时"
                    elif "driver" in stderr_output.lower():
                        failure_reason = "GPU驱动问题"
                    elif "context" in stderr_output.lower():
                        failure_reason = "CUDA上下文问题"
                    elif "surface" in stderr_output.lower():
                        failure_reason = "GPU解码表面问题"
                    elif stderr_output.strip() == "":
                        failure_reason = "FFmpeg进程异常退出(无错误输出)"
                    
                    logging.error(f"  - 失败原因分析: {failure_reason}")
                    
                    # 控制台输出简化的错误信息
                    print(f"GPU失败({failure_reason}) -> CPU处理", flush=True)
                    
                    # 清理临时文件
                    if os.path.exists(temp_output):
                        try:
                            os.remove(temp_output)
                        except:
                            pass
                    
                    # 降级到MoviePy方法
                    logging.info(f"场景 {scene_index} 降级到CPU处理")
                    process_scene_fallback(i, start_frame, end_frame, fps, video_path, output_dir, subtitles, scene_counts)
                    
            except Exception as e:
                # 记录其他异常
                logging.error(f"场景 {scene_index} GPU处理时发生异常:")
                logging.error(f"  - 异常类型: {type(e).__name__}")
                logging.error(f"  - 异常信息: {str(e)}")
                logging.error(f"  - 时间段: {start_time_scene:.2f}s-{end_time_scene:.2f}s")
                
                # 尝试获取系统资源信息
                try:
                    import psutil
                    memory_info = psutil.virtual_memory()
                    logging.error(f"  - 系统内存使用: {memory_info.percent}%")
                    logging.error(f"  - 可用内存: {memory_info.available / (1024**3):.2f}GB")
                except:
                    logging.error("  - 无法获取系统资源信息")
                
                # 尝试获取GPU信息
                try:
                    import subprocess
                    result = subprocess.run(['nvidia-smi', '--query-gpu=memory.used,memory.total,temperature.gpu', 
                                           '--format=csv,noheader,nounits'], 
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        gpu_info = result.stdout.strip().split(',')
                        if len(gpu_info) >= 3:
                            memory_used = gpu_info[0].strip()
                            memory_total = gpu_info[1].strip()
                            temperature = gpu_info[2].strip()
                            logging.error(f"  - GPU显存使用: {memory_used}MB/{memory_total}MB")
                            logging.error(f"  - GPU温度: {temperature}°C")
                except:
                    logging.error("  - 无法获取GPU状态信息")
                
                print(f"GPU异常({type(e).__name__}) -> CPU处理", flush=True)
                
                # 降级到MoviePy方法
                logging.info(f"场景 {scene_index} 因异常降级到CPU处理")
                process_scene_fallback(i, start_frame, end_frame, fps, video_path, output_dir, subtitles, scene_counts)
        else:
            # 无FFmpeg或NVENC支持，使用备用方法
            print(f"处理场景 {scene_index}... (CPU)", flush=True)
            logging.info(f"场景 {scene_index} 使用CPU处理(无GPU支持)")
            process_scene_fallback(i, start_frame, end_frame, fps, video_path, output_dir, subtitles, scene_counts)
        
        # 返回场景数据
        return {
            "scene_id": scene_index,
            "file_name": filename,
            "start_time": format_timestamp(start_time_scene),
            "end_time": format_timestamp(end_time_scene),
            "duration": format_timestamp(duration),
            "subtitle_info": subtitle_info,
            "gpu_accelerated": HAS_FFMPEG and HAS_NVENC
        }
    except Exception as e:
        # 记录顶层异常
        logging.error(f"处理场景 {i+1} 时发生顶层异常:")
        logging.error(f"  - 异常类型: {type(e).__name__}")
        logging.error(f"  - 异常信息: {str(e)}")
        logging.error(f"  - 时间段: {start_time_scene:.2f}s-{end_time_scene:.2f}s")
        
        print(f"场景 {i+1} 处理出错: {str(e)}")
        return {
            "scene_id": i+1,
            "error": str(e),
            "start_time": format_timestamp(start_time_scene),
            "end_time": format_timestamp(end_time_scene)
        }

def process_scene_fallback(i, start_frame, end_frame, fps, video_path, output_dir, subtitles, scene_counts) -> Dict[str, Any]:
    """
    使用MoviePy处理场景（备用方法）
    """
    # 计算时间
    start_time_scene = start_frame / fps
    end_time_scene = end_frame / fps
    duration = end_time_scene - start_time_scene
    
    # 生成文件名 - 现在只使用场景序号
    scene_index = i + 1
    filename = generate_scene_filename(scene_index, start_time_scene, end_time_scene, subtitles, scene_counts)
    output_path = os.path.join(output_dir, filename)
    
    # 提取和保存场景 - 使用原始分辨率的视频
    clip = VideoFileClip(video_path)
    segment_clip = clip.subclip(start_time_scene, end_time_scene)
    
    # 使用更快的编码预设
    segment_clip.write_videofile(
        output_path, 
        codec='libx264', 
        preset='ultrafast',  # 更快的编码速度
        fps=clip.fps,
        verbose=False,
        logger=None,
        threads=2  # 使用多线程编码
    )
    
    # 关闭视频
    segment_clip.close()
    clip.close()
    
    # 获取字幕信息
    subtitle_info = get_subtitle_info(start_time_scene, end_time_scene, subtitles)
    
    # 返回场景数据
    return {
        "scene_id": scene_index,
        "file_name": filename,
        "start_time": format_timestamp(start_time_scene),
        "end_time": format_timestamp(end_time_scene),
        "duration": format_timestamp(duration),
        "subtitle_info": subtitle_info,
        "gpu_accelerated": False
    }

def process_scene_fallback_wrapper(args) -> Dict[str, Any]:
    """
    process_scene_fallback的包装函数，用于接收参数元组
    """
    return process_scene_fallback(*args)

def process_video(video_path: str, srt_path: str, output_dir: str) -> None:
    """处理视频并分割场景"""
    try:
        # 记录开始时间
        start_time = time.time()
        
        # 首先处理字幕文件，生成JSON和TXT格式
        print("步骤 0: 处理字幕文件...")
        try:
            # 检查字幕文件是否存在
            if not os.path.exists(srt_path):
                print("错误: 字幕文件不存在！")
                logging.error(f"字幕文件不存在: {srt_path}")
                return
                
            # 检查字幕文件是否为空
            if os.path.getsize(srt_path) == 0:
                print("错误: 字幕文件为空！")
                logging.error(f"字幕文件为空: {srt_path}")
                # 创建前端提示文件
                with open(os.path.join(output_dir, "error_message.json"), 'w', encoding='utf-8') as f:
                    json.dump({"error": "字幕内容为空！请检查字幕文件。"}, f, ensure_ascii=False, indent=2)
                return
                
            # 处理字幕文件
            text_format, json_format = process_srt(srt_path)
            
            # 检查处理结果是否为空
            if not text_format or not json_format:
                print("错误: 字幕内容解析为空！")
                logging.error(f"字幕内容解析为空: {srt_path}")
                # 创建前端提示文件
                with open(os.path.join(output_dir, "error_message.json"), 'w', encoding='utf-8') as f:
                    json.dump({"error": "字幕内容解析为空！请检查字幕文件格式。"}, f, ensure_ascii=False, indent=2)
                return
                
            # 保存处理后的字幕文件
            base_name = os.path.splitext(os.path.basename(srt_path))[0]
            # 修改保存路径为指定目录
            subtitle_output_dir = "F:\\github\\aicut_auto"
            txt_path = os.path.join(subtitle_output_dir, f"{base_name}.txt")
            json_path = os.path.join(subtitle_output_dir, f"{base_name}.json")
            
            # 确保字幕输出目录存在
            if not os.path.exists(subtitle_output_dir):
                os.makedirs(subtitle_output_dir)
                
            # 确保视频输出目录存在
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
                
            with open(txt_path, 'w', encoding='utf-8') as f:
                f.write(text_format)
                
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(json_format, f, ensure_ascii=False, indent=2)
                
            print(f"字幕处理完成：生成了文本文件 {txt_path} 和 JSON文件 {json_path}")
            logging.info(f"字幕处理完成：生成了文本文件 {txt_path} 和 JSON文件 {json_path}")

            # 合并文本文件功能
            print("步骤 0.1: 开始合并文本文件...")
            try:
                # 定义文件路径
                base_dir = "F:\\github\\aicut_auto"
                script_file = os.path.join(base_dir, "短剧指令2.txt")
                script_long_file = os.path.join(base_dir, "短剧指令长.txt")
                synopsis_file = os.path.join(base_dir, "剧情简介.txt")
                subtitle_file = os.path.join(base_dir, f"{base_name}.txt")  # 使用刚生成的字幕文件
                output_file = os.path.join(base_dir, "短剧指令_新.txt")
                output_long_file = os.path.join(base_dir, "短剧指令_长.txt")

                # 读取短剧指令2.txt（必须存在）
                if not os.path.exists(script_file):
                    error_msg = f"错误：短剧指令2.txt文件不存在！路径：{script_file}"
                    print(error_msg)
                    logging.error(error_msg)
                    return

                with open(script_file, 'r', encoding='utf-8') as f:
                    script_content = f.read().strip()

                # 读取短剧指令长.txt（必须存在）
                if not os.path.exists(script_long_file):
                    error_msg = f"错误：短剧指令长.txt文件不存在！路径：{script_long_file}"
                    print(error_msg)
                    logging.error(error_msg)
                    return

                with open(script_long_file, 'r', encoding='utf-8') as f:
                    script_long_content = f.read().strip()

                # 读取剧情简介.txt（可选）
                synopsis_content = ""
                if os.path.exists(synopsis_file):
                    with open(synopsis_file, 'r', encoding='utf-8') as f:
                        synopsis_content = f.read().strip()
                else:
                    print(f"提示：剧情简介.txt文件不存在，将使用空内容")
                    logging.info(f"剧情简介.txt文件不存在，使用空内容")

                # 读取字幕文件（刚生成的）
                if not os.path.exists(subtitle_file):
                    error_msg = f"错误：字幕文件不存在！路径：{subtitle_file}"
                    print(error_msg)
                    logging.error(error_msg)
                    return

                with open(subtitle_file, 'r', encoding='utf-8') as f:
                    subtitle_content = f.read().strip()

                # 按指定格式组合内容（短剧指令_新.txt）
                combined_content = f"{script_content}\n剧情简介：\n{synopsis_content}\n字幕内容：\n{subtitle_content}"

                # 写入新文件
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(combined_content)

                print(f"文本文件合并完成：{output_file}")
                logging.info(f"文本文件合并完成：{output_file}")

                # 按指定格式组合内容（短剧指令_长.txt）
                combined_long_content = f"{script_long_content}\n剧情简介：\n{synopsis_content}\n字幕内容：\n{subtitle_content}"

                # 写入长版本文件
                with open(output_long_file, 'w', encoding='utf-8') as f:
                    f.write(combined_long_content)

                print(f"文本文件合并完成：{output_long_file}")
                logging.info(f"文本文件合并完成：{output_long_file}")

            except Exception as e:
                error_msg = f"合并文本文件时出错: {str(e)}"
                print(error_msg)
                logging.error(error_msg)
                return

        except Exception as e:
            print(f"处理字幕文件时出错: {str(e)}")
            logging.error(f"处理字幕文件时出错: {str(e)}")
            # 创建前端提示文件
            with open(os.path.join(output_dir, "error_message.json"), 'w', encoding='utf-8') as f:
                json.dump({"error": f"处理字幕文件时出错: {str(e)}"}, f, ensure_ascii=False, indent=2)
            return
        
        # 显示系统信息
        print(f"🚀 系统配置信息:")
        print(f" - CPU核心数: {multiprocessing.cpu_count()}")
        print(f" - 工作线程数: {NUM_WORKERS}")
        print(f" - 帧提取线程数: {FRAME_EXTRACT_WORKERS}")
        print(f" - 帧批处理大小: {FRAME_BATCH_SIZE}")
        print(f" - 场景批处理大小: {SCENE_BATCH_SIZE}")
        print(f" - GPU加速: {'启用' if USE_GPU else '禁用'}")
        print(f" - 帧提取模式: {'CPU模式(MoviePy)' if USE_CPU_EXTRACT else 'GPU解码+CPU缩放混合模式'}")
        print(f" - GPU解码: {'启用' if USE_GPU_DECODE and not USE_CPU_EXTRACT else '禁用'}")
        if GPU_DECODE_INFO['has_nvdec'] and not USE_CPU_EXTRACT:
            print(f" - GPU显存: {GPU_DECODE_INFO['gpu_memory']}MB")
            print(f" - 支持的GPU解码器: {', '.join(GPU_DECODE_INFO['supported_codecs'])}")
        print(f" - NVIDIA硬件编码: {'支持' if HAS_NVENC else '不支持'}")
        print(f" - 场景检测分辨率: {DETECTION_RESOLUTION}")
        print(f" - 视频编码预设: {VIDEO_ENCODE_PRESET} (码率: {VIDEO_BITRATE})")
        print(f" - 场景处理模式: {'FFmpeg批量分割(高效)' if USE_BATCH_SEGMENT else '传统并行处理(兼容)'}")
        if HIGH_PERFORMANCE_MODE:
            print(f" - 高性能模式: 启用 (GPU:{GPU_MAX_CONCURRENT}并发, CPU:{CPU_MAX_CONCURRENT}并发, 批处理:{BATCH_PROCESSING_SIZE})")
        else:
            print(f" - 高性能模式: 禁用 (兼容模式)")
        if USE_CPU_EXTRACT:
            print(f" - 优化模式: CPU并行处理 (MoviePy + {FRAME_EXTRACT_WORKERS}线程)")
        else:
            print(f" - 优化模式: GPU解码+CPU缩放 (最兼容)")
        
        # 详细的GPU状态检查
        if HAS_FFMPEG and HAS_NVENC:
            print(f"\n🔍 详细GPU状态检查:")
            try:
                import subprocess
                
                # 检查GPU基本信息
                result = subprocess.run(['nvidia-smi', '--query-gpu=name,memory.used,memory.total,temperature.gpu,utilization.gpu,power.draw', 
                                       '--format=csv,noheader,nounits'], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    gpu_info = result.stdout.strip().split(',')
                    if len(gpu_info) >= 6:
                        gpu_name = gpu_info[0].strip()
                        memory_used = gpu_info[1].strip()
                        memory_total = gpu_info[2].strip()
                        temperature = gpu_info[3].strip()
                        utilization = gpu_info[4].strip()
                        power_draw = gpu_info[5].strip()
                        
                        print(f" - GPU型号: {gpu_name}")
                        print(f" - 显存使用: {memory_used}MB / {memory_total}MB ({float(memory_used)/float(memory_total)*100:.1f}%)")
                        print(f" - GPU温度: {temperature}°C")
                        print(f" - GPU利用率: {utilization}%")
                        print(f" - 功耗: {power_draw}W")
                        
                        # 记录到日志
                        logging.info(f"程序开始时GPU状态: {gpu_name}, 显存 {memory_used}MB/{memory_total}MB, 温度 {temperature}°C, 利用率 {utilization}%, 功耗 {power_draw}W")
                        
                        # 检查显存是否充足
                        memory_usage_percent = float(memory_used) / float(memory_total) * 100
                        if memory_usage_percent > 80:
                            print(f" ⚠️ 警告: GPU显存使用率已达 {memory_usage_percent:.1f}%，可能影响处理性能")
                            logging.warning(f"GPU显存使用率过高: {memory_usage_percent:.1f}%")
                        
                        # 检查温度
                        if float(temperature) > 80:
                            print(f" ⚠️ 警告: GPU温度较高 ({temperature}°C)，可能影响性能")
                            logging.warning(f"GPU温度过高: {temperature}°C")
                
                # 检查NVENC编码器状态
                result = subprocess.run(['nvidia-smi', 'nvenc', '--query'], 
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    print(f" - NVENC编码器: 可用")
                    logging.info("NVENC编码器状态: 可用")
                else:
                    print(f" - NVENC编码器: 状态未知")
                    logging.info("NVENC编码器状态: 未知")
                    
            except Exception as e:
                print(f" ⚠️ 无法获取详细GPU状态: {str(e)}")
                logging.warning(f"无法获取详细GPU状态: {str(e)}")
        
        # 系统资源检查
        print(f"\n💻 系统资源状态:")
        try:
            import psutil
            
            # 内存信息
            memory_info = psutil.virtual_memory()
            print(f" - 系统内存: {memory_info.used / (1024**3):.1f}GB / {memory_info.total / (1024**3):.1f}GB ({memory_info.percent:.1f}%)")
            
            # CPU信息
            cpu_percent = psutil.cpu_percent(interval=1)
            print(f" - CPU使用率: {cpu_percent:.1f}%")
            
            # 磁盘空间
            disk_usage = psutil.disk_usage(output_dir)
            print(f" - 输出目录磁盘空间: {disk_usage.free / (1024**3):.1f}GB 可用")
            
            # 记录到日志
            logging.info(f"程序开始时系统状态: 内存使用 {memory_info.percent:.1f}%, CPU使用 {cpu_percent:.1f}%, 磁盘可用 {disk_usage.free / (1024**3):.1f}GB")
            
            # 检查资源是否充足
            if memory_info.percent > 80:
                print(f" ⚠️ 警告: 系统内存使用率过高 ({memory_info.percent:.1f}%)")
                logging.warning(f"系统内存使用率过高: {memory_info.percent:.1f}%")
            
            if disk_usage.free < 10 * (1024**3):  # 少于10GB
                print(f" ⚠️ 警告: 磁盘空间不足 ({disk_usage.free / (1024**3):.1f}GB)")
                logging.warning(f"磁盘空间不足: {disk_usage.free / (1024**3):.1f}GB")
                
        except Exception as e:
            print(f" ⚠️ 无法获取系统资源信息: {str(e)}")
            logging.warning(f"无法获取系统资源信息: {str(e)}")
        
        print()  # 空行分隔
        
        # 确保输出目录存在
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            logging.info(f"创建输出目录: {output_dir}")
        
        logging.info(f"开始处理视频: {video_path}")
        print(f"开始处理视频: {video_path}")

        # 添加临时打印语句
        print("步骤 1: 确保输出目录存在完成")

        # 解析字幕文件
        subtitles = parse_srt_file(srt_path)

        # 添加临时打印语句
        print("步骤 2: 解析字幕文件完成")

        if not subtitles:
            logging.error("未能解析字幕文件或字幕文件为空")
            # 添加临时打印语句
            print("错误: 字幕文件解析失败或为空，程序将终止。")
            return

        logging.info(f"成功解析字幕文件，共{len(subtitles)}条字幕")
        print(f"成功解析字幕文件，共{len(subtitles)}条字幕")

        # 初始化TransNetV2模型，指定权重目录
        print("正在加载TransNetV2模型...")
        # 添加临时打印语句
        print("步骤 3: 准备加载TransNetV2模型")
        weights_dir = os.path.join(os.path.dirname(__file__), "视频镜头智能分割-代码", "transnetv2-weights")
        model = TransNetV2(model_dir=weights_dir)
        # 添加临时打印语句
        print("步骤 3: TransNetV2模型加载完成")
        
        # 修改TransNetV2的predict_frames方法，添加自定义进度回调
        original_predict_frames = model.predict_frames
        
        def predict_frames_with_progress(frames: np.ndarray):
            """添加进度条的predict_frames包装函数"""
            def input_iterator():
                # 原始代码的input_iterator部分
                no_padded_frames_start = 25
                no_padded_frames_end = 25 + 50 - (len(frames) % 50 if len(frames) % 50 != 0 else 50)
                
                start_frame = np.expand_dims(frames[0], 0)
                end_frame = np.expand_dims(frames[-1], 0)
                padded_inputs = np.concatenate(
                    [start_frame] * no_padded_frames_start + [frames] + [end_frame] * no_padded_frames_end, 0
                )
                
                ptr = 0
                while ptr + 100 <= len(padded_inputs):
                    out = padded_inputs[ptr:ptr + 100]
                    ptr += 50
                    yield out[np.newaxis]
            
            predictions = []
            
            # 计算总迭代次数用于进度计算
            input_gen = list(input_iterator())
            total_iterations = len(input_gen)
            
            # 如果有GPU加速，尝试提高批处理大小
            batch_predictions = []
            batch_size = SCENE_BATCH_SIZE if USE_GPU else 1  # 使用全局批量大小参数
            current_batch = []
            
            for i, inp in enumerate(input_gen):
                if USE_GPU and len(current_batch) < batch_size:
                    current_batch.append(inp)
                    
                    # 如果批次已满或是最后一个项目，处理批次
                    if len(current_batch) == batch_size or i == len(input_gen) - 1:
                        # 处理当前批次
                        for batch_inp in current_batch:
                            single_frame_pred, all_frames_pred = model.predict_raw(batch_inp)
                            batch_predictions.append((single_frame_pred.numpy()[0, 25:75, 0],
                                             all_frames_pred.numpy()[0, 25:75, 0]))
                        
                        # 清空当前批次
                        current_batch = []
                else:
                    # CPU模式，或GPU但不使用批处理
                    single_frame_pred, all_frames_pred = model.predict_raw(inp)
                    predictions.append((single_frame_pred.numpy()[0, 25:75, 0],
                                    all_frames_pred.numpy()[0, 25:75, 0]))
                
                # 显示带百分比的进度条
                processed_frames = min((i + 1) * 50, len(frames))
                progress = processed_frames / len(frames) * 100
                progress_bar_length = 30
                filled_length = int(progress_bar_length * processed_frames // len(frames))
                bar = '█' * filled_length + '-' * (progress_bar_length - filled_length)
                print(f"\r[TransNetV2] 分析视频场景: [{bar}] {progress:.1f}% ({processed_frames}/{len(frames)}帧)", end="")
            
            # 合并预测结果
            if USE_GPU:
                predictions = batch_predictions
            
            print("\n")
            
            single_frame_pred = np.concatenate([single_ for single_, all_ in predictions])
            all_frames_pred = np.concatenate([all_ for single_, all_ in predictions])
            
            return single_frame_pred[:len(frames)], all_frames_pred[:len(frames)]
        
        # 替换原始方法
        model.predict_frames = predict_frames_with_progress
        
        # 预测视频场景 - 使用低分辨率进行场景检测
        print(f"正在使用分辨率{DETECTION_RESOLUTION}分析视频场景...")
        
        # 使用修改后的predict_video_2方法，传入分辨率参数
        video_frames, single_frame_predictions, all_frame_predictions = predict_video_with_resolution(
            model, video_path, DETECTION_RESOLUTION)
        scenes = model.predictions_to_scenes(single_frame_predictions)
        
        logging.info(f"成功检测到{len(scenes)}个场景")
        print(f"成功检测到{len(scenes)}个场景")

        # 获取视频帧率 - 只需打开一次获取基本信息
        temp_clip = VideoFileClip(video_path)
        fps = temp_clip.fps
        total_duration = temp_clip.duration
        temp_clip.close()

        # 🖼️ 关键帧提取将在场景视频保存完成后进行
        
        # 准备JSON报告
        report = {
            "video_path": video_path,
            "total_duration": format_timestamp(total_duration),
            "total_scenes": len(scenes),
            "scenes": []
        }

        # 关键帧信息将在场景处理完成后添加
        keyframes_info = None
        
        # 用于跟踪每种场景的计数 - 使用线程安全的字典
        from threading import Lock
        scene_counts_lock = Lock()
        scene_counts = {}
        
        # 准备场景处理参数
        scene_params = []
        for i, (start_frame, end_frame) in enumerate(scenes):
            # (i, start_frame, end_frame, fps, video_path, output_dir, subtitles, scene_counts)
            scene_params.append((i, start_frame, end_frame, fps, 
                              video_path, output_dir, subtitles, scene_counts))
        
        # 并行处理场景
        print(f"🚀 开始处理 {len(scenes)} 个场景...")
        
        # 添加GPU处理统计信息
        gpu_success_count = 0
        gpu_failure_count = 0
        
        # 记录开始时的GPU状态
        try:
            import subprocess
            result = subprocess.run(['nvidia-smi', '--query-gpu=memory.used,memory.total,temperature.gpu', 
                                   '--format=csv,noheader,nounits'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                gpu_info = result.stdout.strip().split(',')
                if len(gpu_info) >= 3:
                    memory_used = gpu_info[0].strip()
                    memory_total = gpu_info[1].strip()
                    temperature = gpu_info[2].strip()
                    print(f"📊 处理开始时GPU状态: 显存 {memory_used}MB/{memory_total}MB, 温度 {temperature}°C")
                    logging.info(f"处理开始时GPU状态: 显存 {memory_used}MB/{memory_total}MB, 温度 {temperature}°C")
        except:
            print("⚠️ 无法获取初始GPU状态")
            logging.warning("无法获取初始GPU状态")
        
        # 🚀 选择场景处理模式
        if USE_BATCH_SEGMENT and HAS_FFMPEG:
            print("🎯 选择处理模式: FFmpeg批量分割 (高效模式)")
            results = process_scenes_batch_segment(scenes, fps, video_path, output_dir, subtitles, scene_counts)
        else:
            print("🎯 选择处理模式: 传统并行处理 (兼容模式)")
            results = process_scenes_traditional_parallel(scenes, fps, video_path, output_dir, subtitles, scene_counts)
        
        # 统计GPU处理结果（从批量分割结果中统计）
        for result in results:
            if "error" not in result:
                if result.get("gpu_accelerated", False):
                    gpu_success_count += 1
                else:
                    gpu_failure_count += 1
        
        # 记录最终GPU状态和统计
        try:
            result = subprocess.run(['nvidia-smi', '--query-gpu=memory.used,memory.total,temperature.gpu', 
                                   '--format=csv,noheader,nounits'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                gpu_info = result.stdout.strip().split(',')
                if len(gpu_info) >= 3:
                    memory_used = gpu_info[0].strip()
                    memory_total = gpu_info[1].strip()
                    temperature = gpu_info[2].strip()
                    print(f"📊 处理完成后GPU状态: 显存 {memory_used}MB/{memory_total}MB, 温度 {temperature}°C")
                    logging.info(f"处理完成后GPU状态: 显存 {memory_used}MB/{memory_total}MB, 温度 {temperature}°C")
        except:
            print("⚠️ 无法获取最终GPU状态")
            logging.warning("无法获取最终GPU状态")
        
        # 输出GPU处理统计
        total_processed = gpu_success_count + gpu_failure_count
        if total_processed > 0:
            success_rate = gpu_success_count / total_processed * 100
            print(f"🎯 GPU处理统计: 成功 {gpu_success_count}, 失败 {gpu_failure_count}, 成功率 {success_rate:.1f}%")
            logging.info(f"GPU处理统计: 成功 {gpu_success_count}, 失败 {gpu_failure_count}, 成功率 {success_rate:.1f}%")
            
            # 如果有失败，分析失败模式
            if gpu_failure_count > 0:
                print(f"⚠️ 检测到 {gpu_failure_count} 个GPU处理失败，请查看日志文件了解详细原因")
                logging.warning(f"检测到 {gpu_failure_count} 个GPU处理失败")
                
                # 分析失败的场景序号模式
                failed_scenes = []
                for result in results:
                    if "error" not in result and not result.get("gpu_accelerated", False):
                        failed_scenes.append(result.get("scene_id", 0))
                
                if failed_scenes:
                    failed_scenes.sort()
                    print(f"🔍 失败的场景序号: {failed_scenes}")
                    logging.info(f"失败的场景序号: {failed_scenes}")
                    
                    # 分析连续失败模式
                    consecutive_failures = []
                    current_streak = [failed_scenes[0]]
                    
                    for i in range(1, len(failed_scenes)):
                        if failed_scenes[i] == failed_scenes[i-1] + 1:
                            current_streak.append(failed_scenes[i])
                        else:
                            if len(current_streak) > 1:
                                consecutive_failures.append(current_streak)
                            current_streak = [failed_scenes[i]]
                    
                    if len(current_streak) > 1:
                        consecutive_failures.append(current_streak)
                    
                    if consecutive_failures:
                        print(f"📈 连续失败模式检测到:")
                        logging.info(f"连续失败模式检测到:")
                        for streak in consecutive_failures:
                            print(f"   场景 {streak[0]} 到 {streak[-1]} (连续{len(streak)}个)")
                            logging.info(f"   场景 {streak[0]} 到 {streak[-1]} (连续{len(streak)}个)")
        
        # 处理完成后，重新组织JSON报告结构
        print("整理报告数据结构...")
        
        # 收集字幕间场景的信息，用于平均分配
        between_scenes_data = {}
        
        # 第一遍：收集字幕间场景的信息
        for result in results:
            if "error" in result:
                continue
                
            scene_id = result.get("scene_id")
            subtitle_info = result.get("subtitle_info", {})
            
            # 检查是否是字幕间场景
            if subtitle_info.get("position") == "between" and "between_scenes_metadata" in subtitle_info:
                metadata = subtitle_info["between_scenes_metadata"]
                between_key = metadata.get("between_key")
                
                if between_key:
                    # 初始化数据结构
                    if between_key not in between_scenes_data:
                        between_scenes_data[between_key] = {
                            "scenes": [],
                            "current_subtitle": metadata.get("current_subtitle"),
                            "next_subtitle": metadata.get("next_subtitle")
                        }
                    
                    # 添加场景ID
                    between_scenes_data[between_key]["scenes"].append(scene_id)
        
        # 第二遍：按照平均分配逻辑更新字幕间场景的分配
        for between_key, data in between_scenes_data.items():
            scenes = sorted(data["scenes"])
            total_scenes = len(scenes)
            
            if total_scenes == 0:
                continue
                
            current_subtitle = data["current_subtitle"]
            next_subtitle = data["next_subtitle"]
            
            # 计算分配给前一字幕的场景数（向上取整，确保奇数场景时前字幕多一个）
            first_sub_count = (total_scenes + 1) // 2
            
            print(f"字幕{current_subtitle}和字幕{next_subtitle}之间共有{total_scenes}个场景，前{first_sub_count}个分配给字幕{current_subtitle}，后{total_scenes-first_sub_count}个分配给字幕{next_subtitle}")
            
            # 更新分配信息
            for i, scene_id in enumerate(scenes):
                # 找到对应的结果
                for result in results:
                    if result.get("scene_id") == scene_id:
                        # 确定分配给哪个字幕
                        allocated_subtitle = current_subtitle if i < first_sub_count else next_subtitle
                        
                        # 更新related_subtitles中的allocation标记
                        for sub in result.get("subtitle_info", {}).get("related_subtitles", []):
                            if sub.get("number") == current_subtitle:
                                sub["allocated"] = i < first_sub_count
                            elif sub.get("number") == next_subtitle:
                                sub["allocated"] = i >= first_sub_count
                        
                        # 添加分配信息
                        if "allocation_info" not in result:
                            result["allocation_info"] = {}
                        
                        result["allocation_info"]["allocated_to_subtitle"] = allocated_subtitle
                        result["allocation_info"]["allocation_position"] = i + 1
                        result["allocation_info"]["total_between_scenes"] = total_scenes
                        
                        break
                        
        # 按场景整理的数据（已有的结构）
        scenes_by_id = {}
        for result in results:
            scene_id = result.get("scene_id")
            if scene_id:
                scenes_by_id[scene_id] = result
        
        # 新增：按字幕号整理场景
        subtitles_to_scenes = {}
        
        # 遍历所有场景结果
        for result in results:
            if "error" in result:
                continue
                
            scene_id = result.get("scene_id")
            subtitle_info = result.get("subtitle_info", {})
            related_subtitles = subtitle_info.get("related_subtitles", [])
            
            # 如果是字幕间场景，只考虑已分配的字幕
            if subtitle_info.get("position") == "between" and "allocation_info" in result:
                allocated_subtitle = result["allocation_info"].get("allocated_to_subtitle")
                
                # 只使用分配的字幕
                related_subtitles = [sub for sub in related_subtitles 
                                   if sub.get("number") == allocated_subtitle or 
                                   sub.get("allocated", False)]
            
            # 遍历该场景关联的所有字幕
            for sub in related_subtitles:
                sub_number = sub.get("number")
                if sub_number:
                    # 如果该字幕号不在索引中，初始化
                    if sub_number not in subtitles_to_scenes:
                        subtitles_to_scenes[sub_number] = {
                            "subtitle_number": sub_number,
                            "subtitle_text": sub.get("text", ""),
                            "start_time": sub.get("start_time", ""),
                            "end_time": sub.get("end_time", ""),
                            "related_scenes": []
                        }
                    
                    # 添加场景信息到字幕索引
                    subtitles_to_scenes[sub_number]["related_scenes"].append({
                        "scene_id": scene_id,
                        "file_name": result.get("file_name", ""),
                        "start_time": result.get("start_time", ""),
                        "end_time": result.get("end_time", ""),
                        "duration": result.get("duration", ""),
                        "allocation_info": result.get("allocation_info", {})
                    })
        
        # 将字幕索引转换为列表并按字幕序号排序
        subtitles_index = list(subtitles_to_scenes.values())
        subtitles_index.sort(key=lambda x: x["subtitle_number"])
        
        # 更新报告结构
        report = {
            "video_path": video_path,
            "total_duration": format_timestamp(total_duration),
            "total_scenes": len(scenes),
            "scenes": results,  # 保留原有场景列表
            "subtitles_index": subtitles_index  # 新增字幕索引
        }
        
        # 按场景ID排序结果
        results.sort(key=lambda x: x.get("scene_id", 0))
        report["scenes"] = results

        # 🖼️ 场景视频保存完成后，进行关键帧提取
        if SAVE_KEYFRAMES:
            print("\n🖼️ 场景视频保存完成，开始多线程批量提取关键帧...")
            try:
                keyframes_info = extract_keyframes_from_scene_videos(
                    output_dir, KEYFRAMES_OUTPUT_DIR,
                    image_format=KEYFRAMES_FORMAT, quality=KEYFRAMES_QUALITY,
                    target_resolution=KEYFRAMES_RESOLUTION,
                    keep_aspect_ratio=KEYFRAMES_KEEP_ASPECT_RATIO
                )
                print(f"✅ 关键帧提取完成: 共{keyframes_info['total_keyframes']}张图片")
                logging.info(f"关键帧提取完成: 共{keyframes_info['total_keyframes']}张图片")

                # 添加关键帧信息到报告
                if KEYFRAMES_ADD_TO_REPORT and keyframes_info:
                    report["keyframes_info"] = keyframes_info

            except Exception as e:
                print(f"❌ 关键帧提取失败: {e}")
                logging.error(f"关键帧提取失败: {e}")
                keyframes_info = None
        else:
            print("⏭️ 跳过关键帧提取（SAVE_KEYFRAMES=False）")
            logging.info("跳过关键帧提取（SAVE_KEYFRAMES=False）")

        # 保存JSON报告
        report_path = os.path.join(output_dir, "scenes_report.json")
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 计算并显示总执行时间
        end_time = time.time()
        execution_time = end_time - start_time
        execution_minutes = int(execution_time // 60)
        execution_seconds = execution_time % 60
        
        logging.info(f"报告已保存到 {report_path}")
        logging.info(f"总执行时间: {execution_minutes}分 {execution_seconds:.2f}秒")
        
        print(f"\n所有场景处理完成。报告已保存到 {report_path}")
        print(f"总执行时间: {execution_minutes}分 {execution_seconds:.2f}秒")
        
    except Exception as e:
        logging.error(f"处理视频时出错: {str(e)}", exc_info=True)
        print(f"处理视频时出错: {str(e)}")


def test_gpu_filter_compatibility(video_path: str, resolution=(27, 48)):
    """
    测试不同GPU滤镜链配置的兼容性
    """
    import subprocess
    global GPU_FILTER_CONFIG_CACHE
    
    # 如果已经有缓存的配置，直接使用
    if GPU_FILTER_CONFIG_CACHE is not None:
        print(f"使用缓存的GPU配置: {GPU_FILTER_CONFIG_CACHE['name']}")
        return GPU_FILTER_CONFIG_CACHE
    
    print("正在测试GPU滤镜链兼容性...")
    
    # 测试配置列表，按优先级排序 - 将GPU解码+CPU缩放设为最高优先级
    test_configs = [
        {
            "name": "GPU解码+CPU缩放(最兼容推荐)",
            "cmd": [
                'ffmpeg', '-hwaccel', 'cuda', '-c:v', 'h264_cuvid',
                '-i', video_path,
                '-vf', f'scale={resolution[1]}:{resolution[0]}',
                '-f', 'rawvideo', '-pix_fmt', 'rgb24', '-t', '0.1', '-v', 'error', '-'
            ],
            "priority": 1  # 最高优先级
        },
        {
            "name": "GPU解码+GPU缩放(nv12格式)",
            "cmd": [
                'ffmpeg', '-hwaccel', 'cuda', '-c:v', 'h264_cuvid',
                '-i', video_path,
                '-vf', f'scale_cuda={resolution[1]}:{resolution[0]}:format=nv12',
                '-f', 'rawvideo', '-pix_fmt', 'rgb24', '-t', '0.1', '-v', 'error', '-'
            ],
            "priority": 2
        },
        {
            "name": "GPU解码+指定表面数+GPU缩放",
            "cmd": [
                'ffmpeg', '-hwaccel', 'cuda', '-c:v', 'h264_cuvid', '-surfaces', '8',
                '-i', video_path,
                '-vf', f'scale_cuda={resolution[1]}:{resolution[0]}',
                '-f', 'rawvideo', '-pix_fmt', 'rgb24', '-t', '0.1', '-v', 'error', '-'
            ],
            "priority": 3
        },
        {
            "name": "GPU解码+格式转换+GPU缩放",
            "cmd": [
                'ffmpeg', '-hwaccel', 'cuda', '-c:v', 'h264_cuvid',
                '-i', video_path,
                '-vf', f'hwdownload,format=nv12,hwupload,scale_cuda={resolution[1]}:{resolution[0]}',
                '-f', 'rawvideo', '-pix_fmt', 'rgb24', '-t', '0.1', '-v', 'error', '-'
            ],
            "priority": 4
        },
        {
            "name": "GPU解码+强制yuv420p+GPU缩放",
            "cmd": [
                'ffmpeg', '-hwaccel', 'cuda', '-c:v', 'h264_cuvid',
                '-i', video_path,
                '-vf', f'format=yuv420p,scale_cuda={resolution[1]}:{resolution[0]}',
                '-f', 'rawvideo', '-pix_fmt', 'rgb24', '-t', '0.1', '-v', 'error', '-'
            ],
            "priority": 5
        },
        {
            "name": "GPU解码+混合处理",
            "cmd": [
                'ffmpeg', '-hwaccel', 'cuda', '-c:v', 'h264_cuvid',
                '-i', video_path,
                '-vf', f'hwdownload,scale={resolution[1]}:{resolution[0]},format=rgb24',
                '-f', 'rawvideo', '-pix_fmt', 'rgb24', '-t', '0.1', '-v', 'error', '-'
            ],
            "priority": 6
        }
    ]
    
    # 按优先级排序
    test_configs.sort(key=lambda x: x['priority'])
    
    for config in test_configs:
        try:
            print(f"测试: {config['name']}")
            
            # 运行测试命令
            process = subprocess.run(
                config['cmd'], 
                capture_output=True, 
                timeout=15  # 增加超时时间
            )
            
            if process.returncode == 0 and len(process.stdout) > 0:
                print(f"✓ {config['name']} - 兼容且工作正常")
                
                # 验证输出数据的合理性
                expected_frame_size = resolution[1] * resolution[0] * 3
                actual_size = len(process.stdout)
                
                if actual_size >= expected_frame_size:
                    print(f"  数据验证通过: 输出{actual_size}字节 (期望至少{expected_frame_size}字节)")
                    
                    # 缓存成功的配置
                    GPU_FILTER_CONFIG_CACHE = config
                    return config
                else:
                    print(f"  数据验证失败: 输出{actual_size}字节，小于期望的{expected_frame_size}字节")
            else:
                stderr_output = process.stderr.decode('utf-8', errors='ignore')
                # 只显示关键错误信息
                key_errors = [line for line in stderr_output.split('\n') 
                             if any(keyword in line.lower() for keyword in 
                                   ['error', 'failed', 'impossible', 'not supported'])]
                if key_errors:
                    print(f"✗ {config['name']} - 不兼容: {key_errors[0][:80]}...")
                else:
                    print(f"✗ {config['name']} - 不兼容 (返回码: {process.returncode})")
                
        except subprocess.TimeoutExpired:
            print(f"✗ {config['name']} - 超时")
        except Exception as e:
            print(f"✗ {config['name']} - 错误: {e}")
    
    print("所有GPU滤镜链配置都不兼容，将使用备用方案")
    return None


def get_optimized_gpu_command(video_path: str, resolution=(27, 48), decoder='h264_cuvid'):
    """
    获取优化的GPU命令配置
    """
    # 测试兼容性并获取最佳配置
    compatible_config = test_gpu_filter_compatibility(video_path, resolution)
    
    if compatible_config:
        print(f"使用兼容的GPU配置: {compatible_config['name']}")
        
        # 构建完整的FFmpeg命令
        cmd = compatible_config['cmd'].copy()
        
        # 替换解码器（如果需要）
        for i, arg in enumerate(cmd):
            if arg == 'h264_cuvid' and decoder != 'h264_cuvid':
                cmd[i] = decoder
                break
        
        # 移除测试参数，添加实际参数
        for i, arg in enumerate(cmd):
            if arg == '-t':
                cmd.pop(i)  # 移除-t参数
                cmd.pop(i)  # 移除0.1参数
                break
            if arg == '-v':
                cmd[i+1] = 'warning'  # 改为warning级别
                break
        
        return cmd
    else:
        print("使用传统GPU解码配置")
        # 构建传统FFmpeg命令
        return [
            'ffmpeg',
            '-hwaccel', 'cuda',
            '-hwaccel_device', str(CUDA_DEVICE),
            '-c:v', decoder,
            '-i', video_path,
            '-vf', f'scale_cuda={resolution[1]}:{resolution[0]}',
            '-f', 'rawvideo',
            '-pix_fmt', 'rgb24',
            '-v', 'warning',
            '-'
        ]

def extract_frames_gpu_optimized(video_path: str, resolution=(27, 48), max_frames=None):
    """
    使用FFmpeg + GPU进行超高速帧提取
    """
    import subprocess
    import json
    import threading
    
    # 获取视频信息
    probe_cmd = [
        'ffprobe', '-v', 'quiet', '-print_format', 'json', 
        '-show_streams', video_path
    ]
    
    try:
        result = subprocess.run(probe_cmd, capture_output=True, text=True)
        probe_data = json.loads(result.stdout)
    except Exception as e:
        raise ValueError(f"无法获取视频信息: {e}")
    
    video_stream = None
    for stream in probe_data['streams']:
        if stream['codec_type'] == 'video':
            video_stream = stream
            break
    
    if not video_stream:
        raise ValueError("无法找到视频流")
    
    # 获取视频参数
    try:
        fps_str = video_stream['r_frame_rate']
        fps_parts = fps_str.split('/')
        fps = float(fps_parts[0]) / float(fps_parts[1]) if len(fps_parts) == 2 else float(fps_parts[0])
        duration = float(video_stream['duration'])
        total_frames = int(fps * duration)
        
        # 获取详细的编码信息
        codec_name = video_stream.get('codec_name', 'unknown')
        codec_long_name = video_stream.get('codec_long_name', 'unknown')
        pixel_format = video_stream.get('pix_fmt', 'unknown')
        width = video_stream.get('width', 0)
        height = video_stream.get('height', 0)
        
        print(f"📹 视频编码详细信息:")
        print(f"  - 编码器: {codec_name} ({codec_long_name})")
        print(f"  - 像素格式: {pixel_format}")
        print(f"  - 原始分辨率: {width}x{height}")
        print(f"  - 目标分辨率: {resolution[1]}x{resolution[0]}")
        print(f"  - 帧率: {fps:.2f}fps")
        print(f"  - 时长: {duration:.2f}秒")
        
    except Exception as e:
        print(f"⚠️ 无法精确获取视频参数: {e}")
        # 使用默认值
        fps = 25.0
        duration = 3600.0  # 假设1小时
        total_frames = int(fps * duration)
        codec_name = 'h264'
        pixel_format = 'yuv420p'
    
    if max_frames:
        total_frames = min(total_frames, max_frames)
    
    print(f"🎯 GPU解码任务: {total_frames}帧, {fps:.2f}fps, 时长{duration:.2f}秒")
    
    # 选择最佳解码器
    decoder = 'h264_cuvid'  # 默认
    
    if codec_name == 'h264' and 'h264_cuvid' in GPU_DECODE_INFO['supported_codecs']:
        decoder = 'h264_cuvid'
    elif codec_name == 'hevc' and 'hevc_cuvid' in GPU_DECODE_INFO['supported_codecs']:
        decoder = 'hevc_cuvid'
    elif codec_name == 'av1' and 'av1_cuvid' in GPU_DECODE_INFO['supported_codecs']:
        decoder = 'av1_cuvid'
    else:
        print(f"⚠️ 编码器 {codec_name} 可能与GPU解码器不完全兼容")
    
    # 测试GPU滤镜链兼容性
    print(f"🔧 正在测试GPU滤镜链兼容性...")
    compatible_config = test_gpu_filter_compatibility(video_path, resolution)
    
    if compatible_config:
        print(f"✅ 使用兼容的GPU配置: {compatible_config['name']}")
        
        # 构建完整的FFmpeg命令
        cmd = compatible_config['cmd'].copy()
        # 替换测试参数为实际参数
        for i, arg in enumerate(cmd):
            if arg == '-t':
                cmd.pop(i)  # 移除-t参数
                cmd.pop(i)  # 移除0.1参数
                break
            if arg == '-v':
                cmd[i+1] = 'warning'  # 改为warning级别，减少输出
                break
    else:
        print("⚠️ 使用传统GPU解码配置")
        # 构建传统FFmpeg命令
        cmd = [
            'ffmpeg',
            '-hwaccel', 'cuda',                    # 启用CUDA加速
            '-hwaccel_device', str(CUDA_DEVICE),   # 使用指定GPU
            '-c:v', decoder,                       # 使用GPU解码器
            '-i', video_path,                      # 输入文件
            '-vf', f'scale_cuda={resolution[1]}:{resolution[0]}',  # GPU缩放
            '-f', 'rawvideo',                      # 原始视频格式
            '-pix_fmt', 'rgb24',                   # RGB格式
            '-v', 'warning',                       # 减少日志输出
            '-'                                    # 输出到stdout
        ]
    
    print(f"🚀 启动GPU解码进程...")
    print(f"  使用解码器: {decoder}")
    print(f"  目标分辨率: {resolution}")
    print(f"  预期帧数: {total_frames}")
    print(f"  每帧大小: {resolution[1] * resolution[0] * 3}字节")
    
    # 启动FFmpeg进程 - 使用全局配置的缓冲区大小
    try:
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, 
                                  stderr=subprocess.PIPE, bufsize=GPU_IO_BUFFER_SIZE)
        print(f"✅ FFmpeg进程启动成功 (PID: {process.pid})")
    except Exception as e:
        print(f"❌ 主GPU解码方案启动失败: {e}")
        # 尝试备用方案
        return extract_frames_gpu_fallback(video_path, resolution, max_frames)
    
    frames = []
    frame_size = resolution[1] * resolution[0] * 3  # RGB24
    
    # 优化的状态监控变量
    frame_count = 0
    stderr_output = []
    start_time = time.time()
    last_progress_time = start_time
    progress_interval = 1.0  # 每1秒显示一次进度（提高频率）
    last_frame_count = 0
    monitoring_active = True
    
    # 批量读取配置 - 使用全局参数
    batch_read_size = frame_size * GPU_READ_BUFFER_SIZE  # 使用全局配置
    read_buffer = b''
    
    def status_monitor():
        """状态监控线程函数"""
        nonlocal frame_count, monitoring_active, start_time, total_frames
        
        while monitoring_active:
            time.sleep(15)  # 每15秒检查一次（进一步减少频率）
            if not monitoring_active:
                break
                
            current_time = time.time()
            elapsed = current_time - start_time
            
            if frame_count > 0:
                fps_current = frame_count / elapsed
                progress = frame_count / total_frames * 100
                
                # 计算ETA
                if fps_current > 0:
                    remaining_frames = total_frames - frame_count
                    eta_seconds = remaining_frames / fps_current
                    eta_str = f"ETA: {int(eta_seconds // 60)}分{int(eta_seconds % 60)}秒"
                else:
                    eta_str = "ETA: 计算中..."
                
                print(f"\n💻 状态监控: 已处理{frame_count}/{total_frames}帧 ({progress:.1f}%) - {fps_current:.1f}帧/秒 - {eta_str}")
            else:
                print(f"\n💻 状态监控: GPU解码进行中，已运行{elapsed:.0f}秒...")
    
    # 启动状态监控线程
    monitor_thread = threading.Thread(target=status_monitor, daemon=True)
    monitor_thread.start()
    
    try:
        print(f"📊 开始GPU解码，实时显示进度...")
        
        while frame_count < total_frames:
            # 检查进程是否还在运行
            if process.poll() is not None:
                print(f"\n⚠️ FFmpeg进程已退出，返回码: {process.returncode}")
                break
            
            try:
                # 批量读取数据 - 提高IO效率
                chunk_data = process.stdout.read(batch_read_size)
                
                if not chunk_data:
                    # 没有数据，检查是否有错误信息
                    try:
                        stderr_data = process.stderr.read(8192)  # 读取8KB
                        if stderr_data:
                            stderr_msg = stderr_data.decode('utf-8', errors='ignore')
                            if stderr_msg.strip():
                                # 只记录关键错误信息
                                if any(keyword in stderr_msg.lower() for keyword in ['error', 'failed', 'cannot']):
                                    print(f"\nFFmpeg错误: {stderr_msg.strip()}")
                                stderr_output.append(stderr_msg)
                    except:
                        pass
                    
                    # 等待一小段时间再尝试
                    time.sleep(0.001)  # 减少等待时间
                    continue
                
                # 将新数据添加到缓冲区
                read_buffer += chunk_data
                
                # 批量处理帧数据 - 一次处理多帧
                processed_in_batch = 0
                max_batch_process = GPU_PROCESS_BATCH_SIZE  # 使用全局配置
                
                while len(read_buffer) >= frame_size and frame_count < total_frames and processed_in_batch < max_batch_process:
                    # 提取一帧数据
                    frame_data = read_buffer[:frame_size]
                    read_buffer = read_buffer[frame_size:]
                    
                    # 转换为numpy数组
                    frame = np.frombuffer(frame_data, dtype=np.uint8)
                    frame = frame.reshape((resolution[0], resolution[1], 3))
                    
                    # 确保数据类型正确：TransNetV2期望uint8类型，值范围0-255
                    frames.append(frame)
                    frame_count += 1
                    processed_in_batch += 1
                
                # 优化的进度显示 - 减少频率
                current_time = time.time()
                if current_time - last_progress_time >= progress_interval:
                    elapsed = current_time - start_time
                    fps_current = frame_count / elapsed if elapsed > 0 else 0
                    progress = frame_count / total_frames * 100
                    
                    # 计算这个时间段的帧率
                    frames_this_period = frame_count - last_frame_count
                    period_fps = frames_this_period / progress_interval
                    
                    # 计算ETA
                    if fps_current > 0:
                        remaining_frames = total_frames - frame_count
                        eta_seconds = remaining_frames / fps_current
                        eta_str = f"ETA: {int(eta_seconds // 60)}分{int(eta_seconds % 60)}秒"
                    else:
                        eta_str = "ETA: 计算中..."
                    
                    # 显示进度条
                    progress_bar_length = 30
                    filled_length = int(progress_bar_length * frame_count // total_frames)
                    bar = '█' * filled_length + '-' * (progress_bar_length - filled_length)
                    
                    print(f"\r🎬 GPU解码: [{bar}] {progress:.1f}% ({frame_count}/{total_frames}) - {fps_current:.1f}帧/秒 - {eta_str}", end="", flush=True)
                    
                    last_progress_time = current_time
                    last_frame_count = frame_count
                
                # 每10000帧显示一次详细信息（进一步减少频率）
                if frame_count % 10000 == 0 and frame_count > 0:
                    elapsed = time.time() - start_time
                    fps_current = frame_count / elapsed if elapsed > 0 else 0
                    print(f"\n📈 已处理 {frame_count} 帧，当前速度: {fps_current:.1f}帧/秒")
                
            except Exception as e:
                print(f"\n❌ 读取帧数据时出错: {e}")
                break
        
        print(f"\n")  # 换行
        
        # 停止状态监控
        monitoring_active = False
        
        # 等待进程结束并获取所有stderr输出
        try:
            print(f"🔄 等待FFmpeg进程结束...")
            remaining_stdout, remaining_stderr = process.communicate(timeout=5)
            if remaining_stderr:
                remaining_stderr_str = remaining_stderr.decode('utf-8', errors='ignore')
                if remaining_stderr_str.strip() and any(keyword in remaining_stderr_str.lower() for keyword in ['error', 'warning']):
                    print(f"FFmpeg最终输出: {remaining_stderr_str.strip()}")
                    stderr_output.append(remaining_stderr_str)
        except subprocess.TimeoutExpired:
            print("\n⚠️ FFmpeg进程超时，强制终止...")
            process.kill()
            remaining_stdout, remaining_stderr = process.communicate()
            if remaining_stderr:
                remaining_stderr_str = remaining_stderr.decode('utf-8', errors='ignore')
                if remaining_stderr_str.strip():
                    print(f"FFmpeg终止前输出: {remaining_stderr_str.strip()}")
                    stderr_output.append(remaining_stderr_str)
        
        # 输出关键的FFmpeg信息
        if stderr_output:
            print(f"📋 FFmpeg处理信息:")
            for output in stderr_output[-2:]:  # 只显示最后2条信息
                if output.strip():
                    lines = output.strip().split('\n')
                    for line in lines:
                        if any(keyword in line.lower() for keyword in ['error', 'warning', 'failed']):
                            print(f"  {line}")
        
        elapsed = time.time() - start_time
        print(f"✅ GPU解码完成: {len(frames)}帧, 耗时{elapsed:.2f}秒, 平均速度{len(frames)/elapsed:.1f}帧/秒")
        
        # 如果没有提取到任何帧，尝试备用方案
        if len(frames) == 0:
            print("❌ 主GPU解码方案失败，尝试备用方案...")
            if stderr_output:
                print(f"主方案错误信息: {' '.join(stderr_output[-1:])}")
            return extract_frames_gpu_fallback(video_path, resolution, max_frames)
        
        # 转换为numpy数组，确保数据类型和形状正确
        video_frames = np.array(frames, dtype=np.uint8)
        
        # 验证输出格式
        print(f"🔍 GPU提取帧数据验证:")
        print(f"  - 形状: {video_frames.shape} (期望: [frames, {resolution[0]}, {resolution[1]}, 3])")
        print(f"  - 数据类型: {video_frames.dtype} (期望: uint8)")
        
        # 安全地计算值范围
        if video_frames.size > 0:
            print(f"  - 值范围: {video_frames.min()}-{video_frames.max()} (期望: 0-255)")
        else:
            print(f"  - 值范围: 无数据")
        
        # 确保形状正确
        expected_shape = (len(frames), resolution[0], resolution[1], 3)
        if video_frames.shape != expected_shape:
            raise ValueError(f"GPU提取的帧数据形状错误: {video_frames.shape}, 期望: {expected_shape}")
        
        return video_frames, fps, len(frames)
        
    except Exception as e:
        # 停止状态监控
        monitoring_active = False
        
        # 确保进程被清理
        if process.poll() is None:
            print(f"\n❌ 异常发生，终止FFmpeg进程...")
            process.kill()
        
        # 如果是主GPU方案失败，尝试备用方案
        print(f"❌ 主GPU解码方案出错: {e}")
        print("🔄 尝试备用GPU解码方案...")
        return extract_frames_gpu_fallback(video_path, resolution, max_frames)
    
    finally:
        # 停止状态监控
        monitoring_active = False
        
        # 确保进程被清理
        if process.poll() is None:
            print(f"\n🧹 清理FFmpeg进程...")
            process.terminate()
            try:
                process.wait(timeout=2)
            except subprocess.TimeoutExpired:
                process.kill()


def extract_frames_gpu_fallback(video_path: str, resolution=(27, 48), max_frames=None):
    """
    备用GPU解码方案：使用更简单的FFmpeg命令
    """
    import subprocess
    import json
    
    print("尝试备用GPU解码方案...")
    
    # 获取基本视频信息
    probe_cmd = ['ffprobe', '-v', 'quiet', '-print_format', 'json', '-show_streams', video_path]
    try:
        result = subprocess.run(probe_cmd, capture_output=True, text=True)
        probe_data = json.loads(result.stdout)
        video_stream = next((s for s in probe_data['streams'] if s['codec_type'] == 'video'), None)
        if video_stream:
            fps_str = video_stream['r_frame_rate']
            fps_parts = fps_str.split('/')
            fps = float(fps_parts[0]) / float(fps_parts[1]) if len(fps_parts) == 2 else float(fps_parts[0])
            duration = float(video_stream['duration'])
            total_frames = int(fps * duration)
            if max_frames:
                total_frames = min(total_frames, max_frames)
        else:
            raise ValueError("无法找到视频流")
    except Exception as e:
        print(f"备用方案获取视频信息失败: {e}")
        return None, None, None
    
    # 备用方案1：只使用GPU解码，CPU缩放
    cmd1 = [
        'ffmpeg',
        '-hwaccel', 'cuda',
        '-c:v', 'h264_cuvid',
        '-i', video_path,
        '-vf', f'scale={resolution[1]}:{resolution[0]}',  # CPU缩放
        '-f', 'rawvideo',
        '-pix_fmt', 'rgb24',
        '-v', 'warning',
        '-'
    ]
    
    print(f"备用方案1: GPU解码 + CPU缩放")
    print(f"命令: {' '.join(cmd1)}")
    
    try:
        return _execute_ffmpeg_command(cmd1, resolution, total_frames, fps)
    except Exception as e:
        print(f"备用方案1失败: {e}")
    
    # 备用方案2：完全使用CPU
    cmd2 = [
        'ffmpeg',
        '-i', video_path,
        '-vf', f'scale={resolution[1]}:{resolution[0]}',
        '-f', 'rawvideo',
        '-pix_fmt', 'rgb24',
        '-v', 'warning',
        '-'
    ]
    
    print(f"备用方案2: 完全CPU处理")
    print(f"命令: {' '.join(cmd2)}")
    
    try:
        return _execute_ffmpeg_command(cmd2, resolution, total_frames, fps)
    except Exception as e:
        print(f"备用方案2失败: {e}")
        return None, None, None


def _execute_ffmpeg_command(cmd, resolution, total_frames, fps):
    """
    执行FFmpeg命令并提取帧数据
    """
    import subprocess
    
    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, bufsize=0)
    
    frames = []
    frame_size = resolution[1] * resolution[0] * 3  # RGB24
    stderr_output = []
    
    try:
        start_time = time.time()
        frame_count = 0
        
        while frame_count < total_frames:
            if process.poll() is not None:
                break
                
            frame_data = process.stdout.read(frame_size)
            
            if not frame_data:
                time.sleep(0.001)
                continue
                
            if len(frame_data) != frame_size:
                break
                
            frame = np.frombuffer(frame_data, dtype=np.uint8)
            frame = frame.reshape((resolution[0], resolution[1], 3))
            frames.append(frame)
            frame_count += 1
            
            # 简单进度显示
            if frame_count % 2000 == 0:
                elapsed = time.time() - start_time
                fps_current = frame_count / elapsed if elapsed > 0 else 0
                print(f"\r备用方案进度: {frame_count}/{total_frames} - {fps_current:.1f} 帧/秒", end="")
        
        # 获取stderr输出
        try:
            _, stderr_data = process.communicate(timeout=2)
            if stderr_data:
                stderr_output.append(stderr_data.decode('utf-8', errors='ignore'))
        except subprocess.TimeoutExpired:
            process.kill()
            _, stderr_data = process.communicate()
            if stderr_data:
                stderr_output.append(stderr_data.decode('utf-8', errors='ignore'))
        
        if stderr_output:
            print(f"\n备用方案FFmpeg输出:")
            for output in stderr_output:
                if output.strip():
                    print(f"  {output.strip()}")
        
        elapsed = time.time() - start_time
        print(f"\n备用方案完成: {len(frames)}帧, 耗时{elapsed:.2f}秒")
        
        if len(frames) == 0:
            error_msg = "备用方案也失败：没有提取到任何帧数据"
            if stderr_output:
                error_msg += f"\nFFmpeg错误: {' '.join(stderr_output)}"
            raise RuntimeError(error_msg)
        
        video_frames = np.array(frames, dtype=np.uint8)
        return video_frames, fps, len(frames)
        
    except Exception as e:
        if process.poll() is None:
            process.kill()
        raise e
    finally:
        if process.poll() is None:
            process.terminate()
            try:
                process.wait(timeout=2)
            except subprocess.TimeoutExpired:
                process.kill()


def predict_video_with_resolution(model, video_fn: str, resolution=(27, 48)):
    """
    使用指定的分辨率来预测视频场景
    根据USE_CPU_EXTRACT开关选择处理方式：
    - True: 强制使用CPU模式(MoviePy)
    - False: 优先使用GPU解码+CPU缩放，失败时降级到CPU模式
    """
    print(f"正在以分辨率 {resolution} 加载视频...")
    
    # 检查是否强制使用CPU模式
    if USE_CPU_EXTRACT:
        print("🔧 使用CPU模式进行视频处理...")
        return predict_video_cpu_mode(model, video_fn, resolution)
    
    # 尝试使用GPU加速提取帧
    if USE_GPU_DECODE and GPU_DECODE_INFO['has_nvdec'] and GPU_DECODE_INFO['supported_codecs']:
        try:
            print("尝试使用GPU加速解码...")
            video_frames, fps, total_frames = extract_frames_gpu_optimized(
                video_fn, resolution
            )
            
            print("开始GPU加速场景检测分析...")
            
            # 验证数据格式是否符合TransNetV2要求
            print(f"验证GPU提取的数据格式:")
            print(f"  - 形状: {video_frames.shape}")
            print(f"  - 数据类型: {video_frames.dtype}")
            print(f"  - 值范围: {video_frames.min()}-{video_frames.max()}")
            
            # TransNetV2的predict_frames期望输入格式: [frames, height, width, 3]
            expected_shape = (total_frames, resolution[0], resolution[1], 3)
            if video_frames.shape != expected_shape:
                print(f"警告: GPU提取的帧数据形状不匹配")
                print(f"  实际形状: {video_frames.shape}")
                print(f"  期望形状: {expected_shape}")
                # 尝试修正形状
                if len(video_frames.shape) == 4 and video_frames.shape[1:] == (resolution[0], resolution[1], 3):
                    print("数据形状符合TransNetV2要求，继续处理...")
                else:
                    raise ValueError(f"无法修正GPU提取的数据形状: {video_frames.shape}")
            
            # 确保数据类型正确
            if video_frames.dtype != np.uint8:
                print(f"转换数据类型从 {video_frames.dtype} 到 uint8")
                video_frames = video_frames.astype(np.uint8)
            
            print("开始调用TransNetV2进行场景分析...")
            single_frame_predictions, all_frame_predictions = model.predict_frames(video_frames)
            
            print("GPU加速场景检测完成！")
            return video_frames, single_frame_predictions, all_frame_predictions
            
        except Exception as e:
            print(f"GPU加速失败: {e}")
            print("降级到CPU模式...")
            import traceback
            traceback.print_exc()
    
    # CPU模式 - 调用独立的CPU处理函数
    print("使用CPU模式进行视频处理...")
    return predict_video_cpu_mode(model, video_fn, resolution)


def predict_video_cpu_mode(model, video_fn: str, resolution=(27, 48)):
    """
    CPU模式的视频帧提取和场景检测
    """
    clip = VideoFileClip(video_fn, target_resolution=resolution)
    duration = math.floor(clip.duration * 10) / 10
    fps = clip.fps  # 视频的帧率
    
    # 防止零帧率
    if fps <= 0:
        print("警告: 检测到无效的帧率，将使用默认值25fps")
        fps = 25
        
    total_frames = max(1, int(duration * fps))
    
    print(f"视频信息: 时长 {format_timestamp(duration)}, 帧率 {fps}fps, 总帧数 {total_frames}")
    
    # 检查视频是否有效
    if total_frames <= 0 or duration <= 0:
        raise ValueError(f"无效的视频: 时长 {duration}秒, 总帧数 {total_frames}")
    
    print("开始提取视频帧...")
    
    # 使用更高效的帧提取方法
    frames = []
    # 使用全局批量大小参数
    batch_size = FRAME_BATCH_SIZE
    
    print(f"使用帧提取批量大小: {batch_size}, 场景分析批量大小: {SCENE_BATCH_SIZE}")
    print(f"帧提取并行线程数: {FRAME_EXTRACT_WORKERS}")
    
    # 预先计算所有帧的时间点，避免循环中重复计算
    frame_times = [t/fps for t in range(total_frames)]
    
    # 开始计时
    extract_start_time = time.time()
    
    # 优化的并行批量处理帧
    def extract_frame_batch(batch_frame_times):
        """并行提取一批帧"""
        batch_frames = []
        # 为每个线程创建独立的clip实例，避免线程冲突
        local_clip = VideoFileClip(video_fn, target_resolution=resolution)
        
        try:
            for frame_time in batch_frame_times:
                try:
                    frame = local_clip.get_frame(frame_time)
                    if len(frame) != 0:
                        batch_frames.append(frame)
                except Exception as e:
                    print(f"\n获取帧时出错 (时间: {frame_time:.3f}s): {str(e)}")
                    continue
        finally:
            local_clip.close()
        
        return batch_frames
    
    # 使用线程池进行并行帧提取
    from concurrent.futures import ThreadPoolExecutor, as_completed
    
    # 将帧时间分成更小的子批次用于并行处理
    sub_batch_size = batch_size // FRAME_EXTRACT_WORKERS
    sub_batches = []
    
    for batch_idx in range(0, len(frame_times), batch_size):
        batch_end = min(batch_idx + batch_size, len(frame_times))
        batch_frame_times = frame_times[batch_idx:batch_end]
        
        # 将大批次分成小批次用于并行处理
        for sub_idx in range(0, len(batch_frame_times), sub_batch_size):
            sub_end = min(sub_idx + sub_batch_size, len(batch_frame_times))
            sub_batch = batch_frame_times[sub_idx:sub_end]
            sub_batches.append((batch_idx + sub_idx, sub_batch))
    
    print(f"总计 {len(sub_batches)} 个子批次，每个子批次约 {sub_batch_size} 帧")
    
    # 添加进度监控变量
    import threading
    progress_lock = threading.Lock()
    last_progress_time = extract_start_time
    progress_update_interval = 1.0  # 每秒更新一次进度
    
    # 状态监控线程
    monitoring_active = True
    
    def cpu_status_monitor():
        """CPU提取状态监控线程"""
        nonlocal completed_frames, monitoring_active, extract_start_time, total_frames
        
        while monitoring_active:
            time.sleep(10)  # 每10秒显示一次详细状态
            if not monitoring_active:
                break
                
            with progress_lock:
                current_completed = completed_frames
                
            if current_completed > 0:
                elapsed = time.time() - extract_start_time
                current_fps = current_completed / elapsed if elapsed > 0 else 0
                progress_percent = current_completed / total_frames * 100
                
                # 计算ETA
                if current_fps > 0:
                    remaining_frames = total_frames - current_completed
                    eta_seconds = remaining_frames / current_fps
                    eta_str = f"ETA: {int(eta_seconds // 60)}分{int(eta_seconds % 60)}秒"
                else:
                    eta_str = "ETA: 计算中..."
                
                print(f"\n💻 [CPU提取状态] 已完成{current_completed}/{total_frames}帧 ({progress_percent:.1f}%) - {current_fps:.1f}帧/秒 - {eta_str}")
    
    # 启动状态监控线程
    monitor_thread = threading.Thread(target=cpu_status_monitor, daemon=True)
    monitor_thread.start()
    
    # 使用线程池并行处理
    with ThreadPoolExecutor(max_workers=FRAME_EXTRACT_WORKERS) as executor:
        # 提交所有任务
        future_to_batch = {
            executor.submit(extract_frame_batch, sub_batch): (start_idx, sub_batch)
            for start_idx, sub_batch in sub_batches
        }
        
        # 按顺序收集结果
        batch_results = {}
        completed_frames = 0
        last_display_frames = 0
        
        print(f"🎬 开始CPU并行提取视频帧 ({FRAME_EXTRACT_WORKERS}个线程)...")
        
        for future in as_completed(future_to_batch):
            start_idx, sub_batch = future_to_batch[future]
            try:
                batch_frames = future.result()
                batch_results[start_idx] = batch_frames
                
                with progress_lock:
                    completed_frames += len(batch_frames)
                    current_completed = completed_frames
                
                # 更频繁的进度更新
                current_time = time.time()
                if current_time - last_progress_time >= progress_update_interval or current_completed >= total_frames:
                    # 更新进度条
                    progress = current_completed / total_frames * 100
                    progress_bar_length = 30
                    filled_length = int(progress_bar_length * current_completed // total_frames)
                    bar = '█' * filled_length + '-' * (progress_bar_length - filled_length)
                    
                    # 计算当前帧率
                    elapsed_time = current_time - extract_start_time
                    if elapsed_time > 0:
                        current_fps = current_completed / elapsed_time
                        
                        # 计算这段时间的帧率
                        frames_this_period = current_completed - last_display_frames
                        period_fps = frames_this_period / progress_update_interval if progress_update_interval > 0 else 0
                        
                        # 计算ETA
                        if current_fps > 0:
                            remaining_frames = total_frames - current_completed
                            eta_seconds = remaining_frames / current_fps
                            eta_str = f"ETA: {int(eta_seconds // 60)}分{int(eta_seconds % 60)}秒"
                        else:
                            eta_str = "ETA: 计算中..."
                        
                        # 显示进度
                        print(f"\r🎬 CPU提取: [{bar}] {progress:.1f}% ({current_completed}/{total_frames}) - {current_fps:.1f}帧/秒 - {eta_str}", end="", flush=True)
                        
                        last_progress_time = current_time
                        last_display_frames = current_completed
                
                # 每处理完一定数量的帧显示详细信息
                if current_completed % 2000 == 0 and current_completed > 0:
                    elapsed = time.time() - extract_start_time
                    current_fps = current_completed / elapsed if elapsed > 0 else 0
                    print(f"\n📊 [CPU提取进度] 已处理 {current_completed} 帧，当前速度: {current_fps:.1f}帧/秒")
                
            except Exception as e:
                print(f"\n❌ 处理子批次时出错: {str(e)}")
    
    # 停止状态监控
    monitoring_active = False
    
    print("\n")  # 完成后换行
    
    # 按顺序合并所有帧
    sorted_indices = sorted(batch_results.keys())
    for idx in sorted_indices:
        frames.extend(batch_results[idx])
    
    # 关闭原始clip
    clip.close()
    
    # 检查是否成功提取了帧
    if not frames:
        raise ValueError("无法从视频中提取任何帧")
    
    # 计算总提取时间
    extract_time = time.time() - extract_start_time
    extract_minutes = int(extract_time // 60)
    extract_seconds = extract_time % 60
    
    print(f"\n完成视频帧提取: 共 {len(frames)} 帧, 耗时 {extract_minutes}分 {extract_seconds:.2f}秒")
    print(f"平均提取速度: {len(frames) / extract_time:.1f} 帧/秒")
    
    # 转换为NumPy数组并预测
    print("开始场景检测分析...")
    video = np.array(frames)
    print(f"验证CPU提取的数据格式:")
    print(f"  - 形状: {video.shape}")
    print(f"  - 数据类型: {video.dtype}")
    print(f"  - 值范围: {video.min()}-{video.max()}")
    
    return video, *model.predict_frames(video)


def process_scenes_batch_segment(scenes: List[Tuple[int, int]], fps: float, video_path: str, 
                                output_dir: str, subtitles: List[Dict[str, Any]], 
                                scene_counts: Dict[str, int]) -> List[Dict[str, Any]]:
    """
    高性能FFmpeg多级并行批量处理 - 针对i7-13700K + RTX 4070Ti优化
    采用批处理组 + 高并发 + 智能负载均衡的三级优化架构
    新增：NVENC编码器队列管理，解决硬件编码器并发限制
    """
    print(f"🚀 启动高性能FFmpeg批量分割模式 - 共{len(scenes)}个场景")
    logging.info(f"高性能FFmpeg批量分割开始: {len(scenes)}个场景")
    
    if len(scenes) == 0:
        return []
    
    try:
        # 🎯 第一级：智能配置优化
        is_gpu_mode = HAS_FFMPEG and HAS_NVENC
        
        if HIGH_PERFORMANCE_MODE:
            # 高性能模式：针对NVENC编码器限制优化的并发配置
            max_concurrent = GPU_MAX_CONCURRENT if is_gpu_mode else CPU_MAX_CONCURRENT
            batch_size = BATCH_PROCESSING_SIZE
            print(f"🔥 高性能模式: {max_concurrent}个并发 + {batch_size}个批处理组")
            
            # 新增：NVENC编码器队列管理
            if is_gpu_mode:
                print(f"🎮 NVENC编码器队列管理: 最大{NVENC_MAX_SESSIONS}个同时编码会话")
        else:
            # 兼容模式：保守的并发配置
            max_concurrent = 2 if is_gpu_mode else 6
            batch_size = 8
            print(f"🛡️ 兼容模式: {max_concurrent}个并发 + {batch_size}个批处理组")
        
        # 🎯 第二级：场景信息预处理和分组
        scene_groups = []
        current_group = []
        
        for i, (start_frame, end_frame) in enumerate(scenes):
            start_time = start_frame / fps
            end_time = end_frame / fps
            duration = end_time - start_time
            
            scene_index = i + 1
            filename = generate_scene_filename(scene_index, start_time, end_time, subtitles, scene_counts)
            subtitle_info = get_subtitle_info(start_time, end_time, subtitles)
            
            # 场景信息
            scene_info = {
                "scene_id": scene_index,
                "file_name": filename,
                "start_time": format_timestamp(start_time),
                "end_time": format_timestamp(end_time),
                "duration": format_timestamp(duration),
                "subtitle_info": subtitle_info,
                "start_seconds": start_time,
                "end_seconds": end_time,
                "duration_seconds": duration,
                "output_path": os.path.join(output_dir, filename)
            }
            
            current_group.append(scene_info)
            
            # 当达到批处理大小时，创建新组
            if len(current_group) >= batch_size:
                scene_groups.append(current_group)
                current_group = []
        
        # 添加最后一组（如果有剩余）
        if current_group:
            scene_groups.append(current_group)
        
        print(f"📊 场景组织分析:")
        print(f"  - 总场景数: {len(scenes)}")
        print(f"  - 分组数量: {len(scene_groups)}")
        print(f"  - 平均每组: {len(scenes) / len(scene_groups):.1f}个场景")
        print(f"  - 时间范围: {scenes[0][0]/fps:.2f}s - {scenes[-1][1]/fps:.2f}s")
        print(f"🎮 使用{'GPU加速' if is_gpu_mode else 'CPU'}多级并行处理")
        
        # 🎯 第三级：高并发批处理执行 + NVENC队列管理
        print("⚡ 开始执行高性能多级并行分割...")
        start_time = time.time()
        
        import subprocess
        from concurrent.futures import ThreadPoolExecutor, as_completed
        import threading
        
        # 全局统计变量
        total_scenes = len(scenes)
        completed_scenes = 0
        failed_scenes = []
        results_lock = threading.Lock()
        all_results = []
        
        # 🆕 NVENC编码器队列管理
        nvenc_semaphore = threading.Semaphore(NVENC_MAX_SESSIONS) if is_gpu_mode else None
        nvenc_active_sessions = 0
        nvenc_session_lock = threading.Lock()
        
        def acquire_nvenc_session():
            """获取NVENC编码器会话"""
            nonlocal nvenc_active_sessions
            if nvenc_semaphore:
                nvenc_semaphore.acquire()
                with nvenc_session_lock:
                    nvenc_active_sessions += 1
                    logging.info(f"获取NVENC会话，当前活跃会话: {nvenc_active_sessions}/{NVENC_MAX_SESSIONS}")
        
        def release_nvenc_session():
            """释放NVENC编码器会话"""
            nonlocal nvenc_active_sessions
            if nvenc_semaphore:
                with nvenc_session_lock:
                    nvenc_active_sessions -= 1
                    logging.info(f"释放NVENC会话，当前活跃会话: {nvenc_active_sessions}/{NVENC_MAX_SESSIONS}")
                nvenc_semaphore.release()
        
        def process_scene_group(group_scenes):
            """处理一组场景的工作函数 - 包含NVENC队列管理"""
            group_results = []
            group_failed = []
            
            # 为每组创建独立的FFmpeg命令批次
            ffmpeg_commands = []
            
            for scene_info in group_scenes:
                # 生成优化的FFmpeg命令
                if is_gpu_mode:
                    # GPU加速版本 - 使用更稳定的参数 + NVENC队列管理
                    cmd = [
                        'ffmpeg',
                        '-hwaccel', 'cuda',                    # GPU硬件加速解码
                        '-ss', f"{scene_info['start_seconds']:.3f}",  # 起始时间
                        '-i', video_path,                      # 输入视频
                        '-t', f"{scene_info['duration_seconds']:.3f}",  # 持续时间
                        '-c:v', 'h264_nvenc',                  # NVIDIA硬件编码
                        '-preset', 'fast',                     # 平衡速度和稳定性
                        '-b:v', VIDEO_BITRATE,                 # 视频码率
                        '-c:a', 'aac',                         # 音频编码
                        '-b:a', '128k',                        # 音频码率
                        '-max_muxing_queue_size', '1024',      # 增加复用队列大小
                        '-avoid_negative_ts', 'make_zero',     # 避免负时间戳
                        '-loglevel', 'error',                  # 只输出错误信息
                        '-y',                                  # 覆盖输出文件
                        scene_info['output_path']              # 输出文件
                    ]
                else:
                    # CPU版本 - 使用稳定的快速预设
                    cmd = [
                        'ffmpeg',
                        '-ss', f"{scene_info['start_seconds']:.3f}",    # 起始时间
                        '-i', video_path,                      # 输入视频
                        '-t', f"{scene_info['duration_seconds']:.3f}",  # 持续时间
                        '-c:v', 'libx264',                     # CPU编码
                        '-preset', 'fast',                     # 平衡速度和质量
                        '-crf', '23',                          # 质量设置
                        '-c:a', 'aac',                         # 音频编码
                        '-b:a', '128k',                        # 音频码率
                        '-max_muxing_queue_size', '1024',      # 增加复用队列大小
                        '-avoid_negative_ts', 'make_zero',     # 避免负时间戳
                        '-loglevel', 'error',                  # 只输出错误信息
                        '-y',                                  # 覆盖输出文件
                        scene_info['output_path']              # 输出文件
                    ]
                
                ffmpeg_commands.append((scene_info['scene_id'], cmd, scene_info))
            
            # 在组内使用NVENC队列管理的串行执行（避免编码器冲突）
            for scene_id, cmd, scene_info in ffmpeg_commands:
                success = False
                error_msg = None
                
                # 执行多次重试逻辑
                for attempt in range(NVENC_RETRY_ATTEMPTS + 1):
                    try:
                        if is_gpu_mode:
                            # 获取NVENC编码器会话
                            acquire_nvenc_session()
                        
                        try:
                            result = subprocess.run(
                                cmd,
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE,
                                text=True,
                                timeout=300  # 5分钟超时
                            )
                            
                            if result.returncode == 0:
                                # 验证输出文件存在
                                if os.path.exists(scene_info['output_path']):
                                    success = True
                                    break
                                else:
                                    error_msg = "输出文件未生成"
                            else:
                                # 改进错误信息解析
                                stderr_lines = result.stderr.strip().split('\n')
                                actual_errors = []
                                
                                for line in stderr_lines:
                                    line = line.strip()
                                    # 跳过FFmpeg版本信息和正常输出
                                    if any(skip_pattern in line.lower() for skip_pattern in [
                                        'ffmpeg version',
                                        'built with',
                                        'configuration:',
                                        'lib',
                                        'input #0',
                                        'output #0',
                                        'stream mapping',
                                        'press [q] to stop',
                                        'frame=',
                                        'time=',
                                        'bitrate=',
                                        'speed='
                                    ]):
                                        continue
                                    
                                    # 只保留真正的错误信息
                                    if any(error_pattern in line.lower() for error_pattern in [
                                        'error',
                                        'failed',
                                        'cannot',
                                        'unable',
                                        'invalid',
                                        'not found',
                                        'permission denied',
                                        'no such file',
                                        'out of memory',
                                        'device busy',
                                        'resource temporarily unavailable'
                                    ]):
                                        actual_errors.append(line)
                                
                                # 如果没有真正的错误信息，可能是NVENC资源问题
                                if not actual_errors and attempt < NVENC_RETRY_ATTEMPTS:
                                    error_msg = f"NVENC资源冲突，重试中({attempt+1}/{NVENC_RETRY_ATTEMPTS})"
                                    logging.warning(f"场景{scene_id} NVENC资源冲突，准备重试")
                                elif not actual_errors:
                                    # 检查输出文件是否存在
                                    if os.path.exists(scene_info['output_path']):
                                        success = True
                                        break
                                    else:
                                        error_msg = "输出文件未生成，原因未知"
                                else:
                                    error_msg = '; '.join(actual_errors[:2])  # 只取前2个错误
                        
                        finally:
                            if is_gpu_mode:
                                # 释放NVENC编码器会话
                                release_nvenc_session()
                        
                        # 如果成功或非NVENC错误，跳出重试循环
                        if success or not is_gpu_mode:
                            break
                        
                        # NVENC重试延迟
                        if attempt < NVENC_RETRY_ATTEMPTS:
                            time.sleep(NVENC_RETRY_DELAY)
                            
                    except subprocess.TimeoutExpired:
                        if is_gpu_mode:
                            release_nvenc_session()
                        error_msg = "处理超时(5分钟)"
                        break
                    except Exception as e:
                        if is_gpu_mode:
                            release_nvenc_session()
                        error_msg = f"进程异常: {str(e)[:50]}"
                        break
                
                # 处理结果
                if success:
                    scene_info["gpu_accelerated"] = is_gpu_mode
                    group_results.append(scene_info)
                else:
                    group_failed.append((scene_id, error_msg))
            
            return group_results, group_failed
        
        # 🎯 第四级：组级并行执行（减少并发度，确保NVENC稳定性）
        print(f"🔄 启动{max_concurrent}个并行工作线程处理{len(scene_groups)}个场景组...")
        
        with ThreadPoolExecutor(max_workers=max_concurrent) as executor:
            # 提交所有组处理任务
            group_futures = [executor.submit(process_scene_group, group) for group in scene_groups]
            
            # 实时收集结果和更新进度
            completed_groups = 0
            
            for future in as_completed(group_futures):
                try:
                    group_results, group_failed = future.result()
                    
                    # 线程安全地更新全局结果
                    with results_lock:
                        all_results.extend(group_results)
                        failed_scenes.extend(group_failed)
                        completed_scenes += len(group_results) + len(group_failed)
                        current_completed = completed_scenes
                    
                    completed_groups += 1
                    
                    # 显示实时进度
                    progress = current_completed / total_scenes * 100
                    progress_bar_length = 30
                    filled_length = int(progress_bar_length * current_completed // total_scenes)
                    bar = '█' * filled_length + '-' * (progress_bar_length - filled_length)
                    
                    success_in_group = len(group_results)
                    failed_in_group = len(group_failed)
                    
                    # 显示NVENC会话状态
                    nvenc_status = f"NVENC:{nvenc_active_sessions}/{NVENC_MAX_SESSIONS}" if is_gpu_mode else ""
                    
                    print(f"\r🎬 高性能分割: [{bar}] {progress:.1f}% ({current_completed}/{total_scenes}) "
                          f"组{completed_groups}/{len(scene_groups)} ✓{success_in_group} ✗{failed_in_group} {nvenc_status}", 
                          end="", flush=True)
                    
                except Exception as e:
                    print(f"\n❌ 处理组时出错: {str(e)}")
                    logging.error(f"处理组时出错: {str(e)}")
                    completed_groups += 1
        
        print()  # 换行
        
        # 🎯 第五级：结果统计和优化报告
        elapsed_time = time.time() - start_time
        success_count = len(all_results)
        failed_count = len(failed_scenes)
        success_rate = success_count / total_scenes * 100 if total_scenes > 0 else 0
        
        print(f"✅ 高性能FFmpeg批量分割完成，耗时: {elapsed_time:.2f}秒")
        logging.info(f"高性能FFmpeg批量分割成功，耗时: {elapsed_time:.2f}秒")
        
        # 详细性能统计
        print(f"🎯 高性能分割统计:")
        print(f"  - 成功: {success_count}/{total_scenes} ({success_rate:.1f}%)")
        print(f"  - 失败: {failed_count}个场景")
        print(f"  - 平均每场景: {elapsed_time/total_scenes:.3f}秒")
        print(f"  - 处理速度: {total_scenes/elapsed_time:.1f} 场景/秒")
        print(f"  - 并发配置: {max_concurrent}个主线程 × 串行组内处理 = 稳定性优化")
        print(f"  - 批处理效率: {len(scene_groups)}个组，平均{len(scenes)/len(scene_groups):.1f}场景/组")
        
        if is_gpu_mode:
            print(f"  - NVENC队列管理: 最大{NVENC_MAX_SESSIONS}个同时编码会话，重试{NVENC_RETRY_ATTEMPTS}次")
        
        logging.info(f"高性能批量分割统计: 成功{success_count}/{total_scenes}, 失败{failed_count}, "
                    f"平均{elapsed_time/total_scenes:.3f}秒/场景, 速度{total_scenes/elapsed_time:.1f}场景/秒")
        
        # 失败场景分析
        if failed_scenes:
            print(f"⚠️ 失败场景分析:")
            failure_reasons = {}
            for scene_id, reason in failed_scenes:
                key = reason[:30] + "..." if len(reason) > 30 else reason
                failure_reasons[key] = failure_reasons.get(key, 0) + 1
            
            for reason, count in sorted(failure_reasons.items(), key=lambda x: x[1], reverse=True):
                print(f"   - {reason}: {count}次")
                logging.error(f"失败原因: {reason} ({count}次)")
        
        # 🎯 智能降级策略
        if success_rate < 85:
            print("⚠️ 高性能分割成功率偏低，建议检查NVENC编码器状态或降级到CPU模式...")
            logging.warning(f"高性能分割成功率偏低: {success_rate:.1f}%")
        
        return all_results
        
    except Exception as e:
        print(f"❌ 高性能批量分割准备阶段出错: {str(e)}")
        logging.error(f"高性能批量分割准备阶段出错: {str(e)}")
        
        # 降级到传统并行处理
        print("🔄 降级到传统并行处理模式...")
        return process_scenes_traditional_parallel(scenes, fps, video_path, output_dir, subtitles, scene_counts)


def parse_time_to_seconds(time_str: str) -> float:
    """
    将FFmpeg时间格式(HH:MM:SS.mmm)转换为秒数
    """
    try:
        # 处理可能的格式: HH:MM:SS.mmm 或 MM:SS.mmm 或 SS.mmm
        parts = time_str.strip().split(':')
        
        if len(parts) == 3:
            # HH:MM:SS.mmm
            hours, minutes, seconds = parts
            return int(hours) * 3600 + int(minutes) * 60 + float(seconds)
        elif len(parts) == 2:
            # MM:SS.mmm
            minutes, seconds = parts
            return int(minutes) * 60 + float(seconds)
        else:
            # SS.mmm
            return float(parts[0])
    except:
        return 0.0


def process_scenes_traditional_parallel(scenes: List[Tuple[int, int]], fps: float, video_path: str, 
                                       output_dir: str, subtitles: List[Dict[str, Any]], 
                                       scene_counts: Dict[str, int]) -> List[Dict[str, Any]]:
    """
    传统并行处理方式（作为批量分割的备用方案）
    """
    print(f"📦 传统并行处理模式 - 共{len(scenes)}个场景")
    
    # 根据场景数量动态调整并行数，避免GPU资源竞争
    if len(scenes) <= 50:
        safe_workers = min(NUM_WORKERS, 8)
    elif len(scenes) <= 200:
        safe_workers = min(NUM_WORKERS // 2, 6)
    else:
        safe_workers = min(NUM_WORKERS // 3, 4)
    
    print(f"🔧 使用 {safe_workers} 个并行工作线程")
    
    # 准备场景处理参数
    scene_params = []
    for i, (start_frame, end_frame) in enumerate(scenes):
        scene_params.append((i, start_frame, end_frame, fps, 
                          video_path, output_dir, subtitles, scene_counts))
    
    results = []
    gpu_success_count = 0
    gpu_failure_count = 0
    
    with ThreadPoolExecutor(max_workers=safe_workers) as executor:
        # 根据是否有GPU加速选择不同的处理函数
        process_func = process_scene_gpu if (HAS_FFMPEG and HAS_NVENC) else process_scene_fallback_wrapper
        
        # 提交所有任务
        futures = [executor.submit(process_func, param) for param in scene_params]
        
        # 显示总体进度
        completed = 0
        for future in futures:
            try:
                result = future.result(timeout=300)  # 5分钟超时
                if result:
                    results.append(result)
                    
                    # 统计GPU处理结果
                    if "error" not in result:
                        if result.get("gpu_accelerated", False):
                            gpu_success_count += 1
                        else:
                            gpu_failure_count += 1
                
                completed += 1
                progress = completed / len(scenes) * 100
                progress_bar_length = 30
                filled_length = int(progress_bar_length * completed // len(scenes))
                bar = '█' * filled_length + '-' * (progress_bar_length - filled_length)
                
                # 显示进度和GPU统计
                gpu_stats = f"GPU成功:{gpu_success_count} 失败:{gpu_failure_count}"
                print(f"\r传统并行处理: [{bar}] {progress:.1f}% ({completed}/{len(scenes)}) {gpu_stats}", end="", flush=True)
                
            except Exception as e:
                logging.error(f"传统并行处理场景异常: {str(e)}")
                print(f"\n⚠️ 场景处理异常: {str(e)}")
                completed += 1
    
    print()  # 换行
    
    # 输出统计信息
    total_processed = gpu_success_count + gpu_failure_count
    if total_processed > 0:
        success_rate = gpu_success_count / total_processed * 100
        print(f"🎯 传统处理统计: GPU成功 {gpu_success_count}, 失败 {gpu_failure_count}, 成功率 {success_rate:.1f}%")
        logging.info(f"传统处理统计: GPU成功 {gpu_success_count}, 失败 {gpu_failure_count}, 成功率 {success_rate:.1f}%")
    
    return results


if __name__ == '__main__':
    # 设置默认路径
    default_video_path = "F:\\github\\aicut_auto\\原视频合并\\cutbefore.mp4"
    default_srt_path = "F:\\github\\aicut_auto\\原视频合并\\cutbefore.srt"
    default_output_dir = "F:\\github\\aicut_auto\\ai-video-splitter"
    
    # 可以通过命令行参数设置检测分辨率和并行线程数
    import argparse
    import math  # 添加math模块导入，用于predict_video_with_resolution函数
    
    parser = argparse.ArgumentParser(description='视频场景智能分割工具')
    
    # 文件路径参数
    parser.add_argument('video_path', nargs='?', default=default_video_path,
                        help='输入视频文件路径')
    parser.add_argument('srt_path', nargs='?', default=default_srt_path,
                        help='输入字幕文件路径')
    parser.add_argument('output_dir', nargs='?', default=default_output_dir,
                        help='输出目录路径')
    
    # 性能参数
    parser.add_argument('--width', type=int, default=DETECTION_RESOLUTION[1], 
                        help='场景检测使用的视频宽度 (默认: 48)')
    parser.add_argument('--height', type=int, default=DETECTION_RESOLUTION[0], 
                        help='场景检测使用的视频高度 (默认: 27)')
    parser.add_argument('--threads', type=int, default=NUM_WORKERS,
                        help=f'并行处理场景的线程数 (默认: {NUM_WORKERS})')
    parser.add_argument('--gpu', action='store_true', default=USE_GPU,
                        help='启用GPU加速 (如果可用)')
    parser.add_argument('--no-gpu', action='store_false', dest='gpu',
                        help='禁用GPU加速')
    parser.add_argument('--frame-batch', type=int, default=FRAME_BATCH_SIZE,
                        help=f'视频帧提取批量大小 (默认: {FRAME_BATCH_SIZE})')
    parser.add_argument('--scene-batch', type=int, default=SCENE_BATCH_SIZE,
                        help=f'场景分析批量大小 (默认: {SCENE_BATCH_SIZE})')
    parser.add_argument('--encode-preset', type=str, default=VIDEO_ENCODE_PRESET,
                        help=f'NVIDIA编码预设 p1(最快)-p7(最高质量) (默认: {VIDEO_ENCODE_PRESET})')
    parser.add_argument('--bitrate', type=str, default=VIDEO_BITRATE,
                        help=f'视频码率，如3M、5M、10M (默认: {VIDEO_BITRATE})')
    parser.add_argument('--frame-workers', type=int, default=FRAME_EXTRACT_WORKERS,
                        help=f'帧提取并行线程数 (默认: {FRAME_EXTRACT_WORKERS})')
    parser.add_argument('--gpu-decode', action='store_true', default=USE_GPU_DECODE,
                        help='启用GPU解码加速 (如果可用)')
    parser.add_argument('--no-gpu-decode', action='store_false', dest='gpu_decode',
                        help='禁用GPU解码加速')
    parser.add_argument('--cuda-device', type=int, default=CUDA_DEVICE,
                        help=f'CUDA设备ID (默认: {CUDA_DEVICE})')
    parser.add_argument('--cpu-extract', action='store_true', default=USE_CPU_EXTRACT,
                        help='强制使用CPU提取帧(MoviePy模式)，忽略GPU解码设置')
    parser.add_argument('--no-cpu-extract', action='store_false', dest='cpu_extract',
                        help='使用GPU解码+CPU缩放混合方案(默认)')
    parser.add_argument('--batch-segment', action='store_true', default=USE_BATCH_SEGMENT,
                        help='使用FFmpeg批量分割模式(默认，性能更高)')
    parser.add_argument('--no-batch-segment', action='store_false', dest='batch_segment',
                        help='使用传统并行处理模式(兼容性更好)')
    parser.add_argument('--high-performance', action='store_true', default=HIGH_PERFORMANCE_MODE,
                        help='启用高性能模式(更多并发，更快处理)')
    parser.add_argument('--no-high-performance', action='store_false', dest='high_performance',
                        help='禁用高性能模式(兼容性更好)')
    parser.add_argument('--gpu-concurrent', type=int, default=GPU_MAX_CONCURRENT,
                        help=f'GPU模式最大并发数 (默认: {GPU_MAX_CONCURRENT})')
    parser.add_argument('--cpu-concurrent', type=int, default=CPU_MAX_CONCURRENT,
                        help=f'CPU模式最大并发数 (默认: {CPU_MAX_CONCURRENT})')
    parser.add_argument('--batch-size', type=int, default=BATCH_PROCESSING_SIZE,
                        help=f'批处理组大小 (默认: {BATCH_PROCESSING_SIZE})')

    # 关键帧保存参数
    parser.add_argument('--save-keyframes', action='store_true', default=SAVE_KEYFRAMES,
                        help='保存场景关键帧')
    parser.add_argument('--no-save-keyframes', action='store_false', dest='save_keyframes',
                        help='不保存场景关键帧')
    parser.add_argument('--keyframes-dir', type=str, default=KEYFRAMES_OUTPUT_DIR,
                        help=f'关键帧输出目录 (默认: {KEYFRAMES_OUTPUT_DIR})')
    parser.add_argument('--keyframes-format', type=str, default=KEYFRAMES_FORMAT,
                        choices=['PNG', 'JPG', 'JPEG'],
                        help=f'关键帧图片格式 (默认: {KEYFRAMES_FORMAT})')
    parser.add_argument('--keyframes-quality', type=int, default=KEYFRAMES_QUALITY,
                        help=f'JPG图片质量1-100 (默认: {KEYFRAMES_QUALITY})')
    parser.add_argument('--keyframes-resolution', type=str, default=str(KEYFRAMES_RESOLUTION),
                        help=f'关键帧分辨率: "original"或"1920x1080"格式 (默认: {KEYFRAMES_RESOLUTION})')
    parser.add_argument('--no-keep-aspect-ratio', action='store_false', dest='keep_aspect_ratio',
                        default=KEYFRAMES_KEEP_ASPECT_RATIO,
                        help='不保持宽高比（默认保持）')

    args = parser.parse_args()

    # 解析关键帧分辨率参数
    def parse_resolution(resolution_str):
        """解析分辨率字符串"""
        if resolution_str.lower() == "original":
            return "original"

        # 尝试解析 "1920x1080" 格式
        if 'x' in resolution_str.lower():
            try:
                width, height = resolution_str.lower().split('x')
                return (int(width), int(height))
            except ValueError:
                print(f"⚠️ 无效的分辨率格式: {resolution_str}，使用默认值")
                return KEYFRAMES_RESOLUTION

        return KEYFRAMES_RESOLUTION

    # 使用参数中的路径
    video_path = args.video_path
    srt_path = args.srt_path
    output_dir = args.output_dir
    
    # 更新全局参数设置
    DETECTION_RESOLUTION = (args.height, args.width)
    NUM_WORKERS = args.threads
    USE_GPU = args.gpu and HAS_GPU  # 只有当命令行指定GPU且GPU可用时才启用
    FRAME_BATCH_SIZE = args.frame_batch
    FRAME_EXTRACT_WORKERS = args.frame_workers
    USE_GPU_DECODE = args.gpu_decode
    # 修复：只有当命令行明确指定时才覆盖全局变量，否则保持文件中的设置
    if '--cpu-extract' in sys.argv or '--no-cpu-extract' in sys.argv:
        USE_CPU_EXTRACT = args.cpu_extract
    # 如果没有命令行参数，USE_CPU_EXTRACT 保持文件中的设置不变
    if '--batch-segment' in sys.argv or '--no-batch-segment' in sys.argv:
        USE_BATCH_SEGMENT = args.batch_segment
    # 如果没有命令行参数，USE_BATCH_SEGMENT 保持文件中的设置不变
    if '--high-performance' in sys.argv or '--no-high-performance' in sys.argv:
        HIGH_PERFORMANCE_MODE = args.high_performance
    if '--gpu-concurrent' in sys.argv:
        GPU_MAX_CONCURRENT = args.gpu_concurrent
    if '--cpu-concurrent' in sys.argv:
        CPU_MAX_CONCURRENT = args.cpu_concurrent
    if '--batch-size' in sys.argv:
        BATCH_PROCESSING_SIZE = args.batch_size
    CUDA_DEVICE = args.cuda_device
    SCENE_BATCH_SIZE = args.scene_batch
    VIDEO_ENCODE_PRESET = args.encode_preset
    VIDEO_BITRATE = args.bitrate

    # 更新关键帧相关参数
    if '--save-keyframes' in sys.argv or '--no-save-keyframes' in sys.argv:
        SAVE_KEYFRAMES = args.save_keyframes
    if '--keyframes-dir' in sys.argv:
        KEYFRAMES_OUTPUT_DIR = args.keyframes_dir
    if '--keyframes-format' in sys.argv:
        KEYFRAMES_FORMAT = args.keyframes_format
    if '--keyframes-quality' in sys.argv:
        KEYFRAMES_QUALITY = args.keyframes_quality
    if '--keyframes-resolution' in sys.argv:
        KEYFRAMES_RESOLUTION = parse_resolution(args.keyframes_resolution)
    if '--no-keep-aspect-ratio' in sys.argv:
        KEYFRAMES_KEEP_ASPECT_RATIO = args.keep_aspect_ratio

    # 打印使用的路径
    print(f"视频文件: {video_path}")
    print(f"字幕文件: {srt_path}")
    print(f"输出目录: {output_dir}")
    
    process_video(video_path, srt_path, output_dir) 