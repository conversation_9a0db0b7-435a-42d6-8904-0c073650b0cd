2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  90%|████████▉ | 96/107 [00:01<00:00, 52.15it/s][A[A[A[A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  12%|█▏        | 14/117 [00:00<00:01, 56.10it/s]
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  90%|█████████ | 95/105 [00:01<00:00, 51.47it/s][A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:   9%|▉         | 11/125 [00:00<00:02, 47.13it/s][A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  76%|███████▌  | 65/86 [00:01<00:00, 43.15it/s][A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  12%|█▏        | 11/92 [00:00<00:01, 51.94it/s][A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:   4%|▍         | 5/120 [00:00<00:02, 49.93it/s][A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  94%|█████████▍| 107/114 [00:02<00:00, 47.03it/s][A[A[A[A[A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  54%|█████▍    | 59/109 [00:01<00:01, 48.11it/s][A[A[A✓ 视频 38_1 合成和音频添加完成: F:/github/aicut_auto/water_remove\38_1_remove_with_audio.mp4
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: --- 开始合成视频: 9_1 ---
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: === 开始视频合成: F:/github/aicut_auto/water_remove\9_1_remove.mp4 ===
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 帧文件数量: 119
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 分辨率: 720x1280, 帧率: 25.0
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次大小: 119
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:   0%|          | 0/119 [00:00<?, ?it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  41%|████      | 41/100 [00:00<00:01, 49.84it/s][A[A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  79%|███████▉  | 72/91 [00:01<00:00, 48.25it/s][A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  95%|█████████▌| 102/107 [00:01<00:00, 52.17it/s][A[A[A[A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  17%|█▋        | 20/117 [00:00<00:01, 55.53it/s]
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  13%|█▎        | 16/125 [00:00<00:02, 46.74it/s][A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  96%|█████████▌| 101/105 [00:02<00:00, 50.67it/s][A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  98%|█████████▊| 112/114 [00:02<00:00, 47.19it/s][A[A[A[A[A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  81%|████████▏ | 70/86 [00:01<00:00, 42.57it/s][A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  18%|█▊        | 17/92 [00:00<00:01, 51.61it/s][A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  60%|█████▉    | 65/109 [00:01<00:00, 50.38it/s][A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:   5%|▌         | 6/119 [00:00<00:02, 55.77it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:   9%|▉         | 11/120 [00:00<00:02, 49.29it/s][A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  47%|████▋     | 47/100 [00:00<00:01, 49.73it/s][A[A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1: 100%|██████████| 114/114 [00:02<00:00, 45.23it/s]
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  86%|████████▌ | 78/91 [00:01<00:00, 48.45it/s][A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1: 100%|██████████| 105/105 [00:02<00:00, 49.69it/s]
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1: 100%|██████████| 107/107 [00:02<00:00, 52.90it/s]
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  17%|█▋        | 21/125 [00:00<00:02, 46.74it/s][A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  22%|██▏       | 26/117 [00:00<00:01, 50.53it/s]
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  13%|█▎        | 16/120 [00:00<00:02, 47.09it/s][A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  87%|████████▋ | 75/86 [00:01<00:00, 41.48it/s][A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  25%|██▌       | 23/92 [00:00<00:01, 49.35it/s][A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  52%|█████▏    | 52/100 [00:01<00:00, 48.74it/s][A[A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  65%|██████▌   | 71/109 [00:01<00:00, 48.82it/s][A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  10%|█         | 12/119 [00:00<00:02, 48.73it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  92%|█████████▏| 84/91 [00:01<00:00, 49.47it/s][A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  21%|██        | 26/125 [00:00<00:02, 45.71it/s][A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  27%|██▋       | 32/117 [00:00<00:01, 49.59it/s]
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  18%|█▊        | 21/120 [00:00<00:02, 47.26it/s][A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  32%|███▏      | 29/92 [00:00<00:01, 51.32it/s][A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  93%|█████████▎| 80/86 [00:01<00:00, 41.90it/s][A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  71%|███████   | 77/109 [00:01<00:00, 49.92it/s][A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  58%|█████▊    | 58/100 [00:01<00:00, 49.63it/s][A[A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  15%|█▌        | 18/119 [00:00<00:02, 49.34it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  99%|█████████▉| 90/91 [00:01<00:00, 51.31it/s][A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1: 100%|██████████| 91/91 [00:01<00:00, 46.72it/s]
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  26%|██▌       | 32/125 [00:00<00:01, 47.84it/s][A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  32%|███▏      | 37/117 [00:00<00:01, 49.16it/s]
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  22%|██▎       | 27/120 [00:00<00:01, 49.11it/s][A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  99%|█████████▉| 85/86 [00:01<00:00, 43.26it/s][A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  38%|███▊      | 35/92 [00:00<00:01, 51.70it/s][A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  63%|██████▎   | 63/100 [00:01<00:00, 49.34it/s][A[A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1: 100%|██████████| 86/86 [00:01<00:00, 43.55it/s]
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  77%|███████▋  | 84/109 [00:01<00:00, 52.66it/s][A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  20%|██        | 24/119 [00:00<00:01, 50.53it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  30%|███       | 38/125 [00:00<00:01, 50.17it/s][A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  36%|███▌      | 42/117 [00:00<00:01, 49.08it/s]
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  28%|██▊       | 33/120 [00:00<00:01, 50.26it/s][A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  69%|██████▉   | 69/100 [00:01<00:00, 51.58it/s][A[A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  45%|████▍     | 41/92 [00:00<00:00, 53.17it/s][A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  83%|████████▎ | 90/109 [00:01<00:00, 52.86it/s][A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  25%|██▌       | 30/119 [00:00<00:01, 50.95it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:46 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  35%|███▌      | 44/125 [00:00<00:01, 52.75it/s][A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  40%|████      | 47/117 [00:00<00:01, 48.98it/s]
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  32%|███▎      | 39/120 [00:00<00:01, 52.02it/s][A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  75%|███████▌  | 75/100 [00:01<00:00, 53.14it/s][A[A[A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  51%|█████     | 47/92 [00:00<00:00, 53.29it/s][A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  89%|████████▉ | 97/109 [00:01<00:00, 55.79it/s][A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  30%|███       | 36/119 [00:00<00:01, 50.33it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  40%|████      | 50/125 [00:00<00:01, 54.34it/s][A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  44%|████▍     | 52/117 [00:01<00:01, 48.27it/s]
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  38%|███▊      | 45/120 [00:00<00:01, 54.29it/s][A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  81%|████████  | 81/100 [00:01<00:00, 53.41it/s][A[A[A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  58%|█████▊    | 53/92 [00:01<00:00, 52.77it/s][A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  94%|█████████▍| 103/109 [00:02<00:00, 55.66it/s][A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  35%|███▌      | 42/119 [00:00<00:01, 51.37it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  45%|████▍     | 56/125 [00:01<00:01, 54.47it/s][A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  49%|████▊     | 57/117 [00:01<00:01, 47.89it/s]
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  42%|████▎     | 51/120 [00:00<00:01, 55.27it/s][A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  87%|████████▋ | 87/100 [00:01<00:00, 53.61it/s][A[A[A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  64%|██████▍   | 59/92 [00:01<00:00, 54.13it/s][A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1: 100%|██████████| 109/109 [00:02<00:00, 56.22it/s][A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1: 100%|██████████| 109/109 [00:02<00:00, 50.69it/s]
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  50%|████▉     | 62/125 [00:01<00:01, 55.19it/s][A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  40%|████      | 48/119 [00:00<00:01, 51.92it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  53%|█████▎    | 62/117 [00:01<00:01, 47.24it/s]
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  48%|████▊     | 57/120 [00:01<00:01, 54.25it/s][A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  93%|█████████▎| 93/100 [00:01<00:00, 54.33it/s][A[A[A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  71%|███████   | 65/92 [00:01<00:00, 55.64it/s][A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  54%|█████▍    | 68/125 [00:01<00:01, 54.40it/s][A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  45%|████▌     | 54/119 [00:01<00:01, 51.90it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  58%|█████▊    | 68/117 [00:01<00:00, 49.46it/s]
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  53%|█████▎    | 64/120 [00:01<00:00, 57.73it/s][A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  99%|█████████▉| 99/100 [00:01<00:00, 54.88it/s][A[A[A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  77%|███████▋  | 71/92 [00:01<00:00, 55.80it/s][A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1: 100%|██████████| 100/100 [00:01<00:00, 51.75it/s]
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  59%|█████▉    | 74/125 [00:01<00:00, 53.77it/s][A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  62%|██████▏   | 73/117 [00:01<00:00, 49.19it/s]
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  50%|█████     | 60/119 [00:01<00:01, 52.12it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  59%|█████▉    | 71/120 [00:01<00:00, 59.80it/s][A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  85%|████████▍ | 78/92 [00:01<00:00, 57.34it/s][A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  64%|██████▍   | 80/125 [00:01<00:00, 54.75it/s][A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  68%|██████▊   | 79/117 [00:01<00:00, 49.84it/s]
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  56%|█████▋    | 67/119 [00:01<00:00, 54.62it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  64%|██████▍   | 77/120 [00:01<00:00, 59.49it/s][A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  91%|█████████▏| 84/92 [00:01<00:00, 57.98it/s][A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  69%|██████▉   | 86/125 [00:01<00:00, 56.05it/s][A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  62%|██████▏   | 74/119 [00:01<00:00, 57.46it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  73%|███████▎  | 85/117 [00:01<00:00, 50.93it/s]
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  69%|██████▉   | 83/120 [00:01<00:00, 59.50it/s][A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  98%|█████████▊| 90/92 [00:01<00:00, 58.52it/s][A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1: 100%|██████████| 92/92 [00:01<00:00, 54.60it/s]
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  74%|███████▎  | 92/125 [00:01<00:00, 56.77it/s][A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  79%|███████▊  | 92/117 [00:01<00:00, 55.60it/s]
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  68%|██████▊   | 81/119 [00:01<00:00, 59.81it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  75%|███████▌  | 90/120 [00:01<00:00, 61.31it/s][A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  79%|███████▉  | 99/125 [00:01<00:00, 58.01it/s][A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  74%|███████▍  | 88/119 [00:01<00:00, 61.70it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  85%|████████▌ | 100/117 [00:01<00:00, 60.29it/s]
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:47 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  81%|████████  | 97/120 [00:01<00:00, 62.69it/s][A[A[A[A[A[A
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  85%|████████▍ | 106/125 [00:01<00:00, 58.79it/s][A
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  80%|███████▉  | 95/119 [00:01<00:00, 63.76it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  91%|█████████▏| 107/117 [00:02<00:00, 62.81it/s]
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  87%|████████▋ | 104/120 [00:01<00:00, 61.23it/s][A[A[A[A[A[A
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  90%|████████▉ | 112/125 [00:02<00:00, 58.98it/s][A
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  87%|████████▋ | 103/119 [00:01<00:00, 65.79it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  97%|█████████▋| 114/117 [00:02<00:00, 62.65it/s]
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  92%|█████████▎| 111/120 [00:01<00:00, 61.89it/s][A[A[A[A[A[A
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1: 100%|██████████| 117/117 [00:02<00:00, 54.11it/s]
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  94%|█████████▍| 118/125 [00:02<00:00, 58.41it/s][A
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  92%|█████████▏| 110/119 [00:01<00:00, 65.54it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  98%|█████████▊| 118/120 [00:02<00:00, 62.45it/s][A[A[A[A[A[A
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1: 100%|██████████| 120/120 [00:02<00:00, 57.10it/s]
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1: 100%|██████████| 125/125 [00:02<00:00, 59.32it/s][A
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1: 100%|██████████| 125/125 [00:02<00:00, 54.48it/s]
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1:  98%|█████████▊| 117/119 [00:02<00:00, 66.68it/s][A[A[A[A[A[A[A[A[A
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 写入批次 1: 100%|██████████| 119/119 [00:02<00:00, 57.66it/s]
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 39_1 合成和音频添加完成: F:/github/aicut_auto/water_remove\39_1_remove_with_audio.mp4
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 37_1 合成和音频添加完成: F:/github/aicut_auto/water_remove\37_1_remove_with_audio.mp4
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 34_1 合成和音频添加完成: F:/github/aicut_auto/water_remove\34_1_remove_with_audio.mp4
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 34_1 完整处理成功
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 35_1 完整处理成功
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 36_1 完整处理成功
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 37_1 完整处理成功
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 38_1 完整处理成功
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 39_1 完整处理成功
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 3_1 合成和音频添加完成: F:/github/aicut_auto/water_remove\3_1_remove_with_audio.mp4
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 3_1 完整处理成功
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 40_1 合成和音频添加完成: F:/github/aicut_auto/water_remove\40_1_remove_with_audio.mp4
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 40_1 完整处理成功
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 41_1 合成和音频添加完成: F:/github/aicut_auto/water_remove\41_1_remove_with_audio.mp4
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 41_1 完整处理成功
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 4_1 合成和音频添加完成: F:/github/aicut_auto/water_remove\4_1_remove_with_audio.mp4
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 4_1 完整处理成功
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 7_1 合成和音频添加完成: F:/github/aicut_auto/water_remove\7_1_remove_with_audio.mp4
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 5_1 合成和音频添加完成: F:/github/aicut_auto/water_remove\5_1_remove_with_audio.mp4
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 5_1 完整处理成功
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 8_1 合成和音频添加完成: F:/github/aicut_auto/water_remove\8_1_remove_with_audio.mp4
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 6_1 合成和音频添加完成: F:/github/aicut_auto/water_remove\6_1_remove_with_audio.mp4
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 6_1 完整处理成功
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 7_1 完整处理成功
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 8_1 完整处理成功
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 9_1 合成和音频添加完成: F:/github/aicut_auto/water_remove\9_1_remove_with_audio.mp4
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: ✓ 视频 9_1 完整处理成功
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: === 并行视频合成完成 ===
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 成功合成视频数: 41/41
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 成功添加音频数: 41/41
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: === 合成和音频添加阶段完成 ===
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 成功处理视频数: 41/41
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: === 视频处理流程全部完成 ===
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: LaMa模型资源清理完成
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 程序总执行耗时: 553.62 秒
2025-08-15 17:32:48 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 程序总执行耗时: 9 分 13.62 秒
2025-08-15 17:32:49 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:270 - watermark_removal2.py 脚本执行成功
2025-08-15 17:32:49 - aicut_auto.core.watermark_remover - INFO - remove_watermark:85 - 水印去除任务完成
2025-08-15 17:34:15 - aicut_auto.CapCutImporter - INFO - import_to_capcut:36 - 开始CapCut导入任务
2025-08-15 17:34:15 - aicut_auto.CapCutImporter - INFO - _build_command_args:203 - 构建的命令行参数: --fps 30
2025-08-15 17:34:15 - aicut_auto.CapCutImporter - INFO - _execute_script_direct:209 - 开始执行CapCut导入脚本
2025-08-15 17:34:15 - aicut_auto.CapCutImporter - INFO - _execute_script_direct:212 - 正在导入 close_capcut 模块...
2025-08-15 17:34:15 - aicut_auto.CapCutImporter - INFO - _execute_script_direct:225 - 尝试关闭 CapCut 窗口...
2025-08-15 17:34:15 - aicut_auto.CapCutImporter - INFO - _execute_script_direct:229 - CapCut 窗口已关闭
2025-08-15 17:34:15 - aicut_auto.CapCutImporter - INFO - _execute_script_direct:236 - 正在导入 5-1_generate_capcut_json.py...
2025-08-15 17:36:01 - aicut_auto.CapCutImporter - INFO - _execute_script_direct:259 - CapCut JSON文件生成成功: F:\github\aicut_auto\草稿模板\draft_content.json
2025-08-15 17:36:01 - aicut_auto.CapCutImporter - INFO - import_to_capcut:53 - CapCut导入任务完成
2025-08-15 19:53:39 - aicut_auto - INFO - _log_startup:101 - 日志系统已初始化
2025-08-15 19:53:39 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-15 19:53:39 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-15 19:53:39 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-15 19:53:39 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-15 19:53:39 - aicut_auto - INFO - _log_startup:101 - 日志系统已初始化
2025-08-15 19:53:40 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-15 19:53:40 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-15 19:53:40 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-15 19:53:40 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-15 19:53:40 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-15 19:53:40 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-15 19:53:40 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-15 19:53:40 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-15 19:53:40 - aicut_auto - INFO - startup_event:811 - === AI Cut Auto Backend 启动 ===
2025-08-15 19:53:40 - aicut_auto - INFO - startup_event:812 - 项目根目录: F:\github\aicut_auto
2025-08-15 19:53:40 - aicut_auto - INFO - startup_event:814 - 任务管理器已初始化
2025-08-15 19:53:40 - aicut_auto - INFO - startup_event:818 - 全局进程跟踪器已初始化
2025-08-15 19:53:40 - aicut_auto - INFO - startup_event:820 - === 后端服务启动完成 ===
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - __init__:36 - AI视频分割器已启用进程跟踪，task_id: a68ea5c0-d83a-4138-8323-538b3f2c51d2
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - split_video:61 - 开始AI视频分割任务，参数: {'inputVideoPath': 'F:\\github\\aicut_auto\\原视频合并\\cutbefore.mp4', 'outputDirPath': 'F:\\github\\aicut_auto\\ai-video-splitter', 'frameBatchSize': 2000, 'sceneBatchSize': 500, 'videoEncodePreset': 'p4', 'videoBitrate': '5M', 'processMode': 'fast'}
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:129 - AI视频分割开始:
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:130 -   输入视频: F:\github\aicut_auto\原视频合并\cutbefore.mp4
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:131 -   输出目录: F:\github\aicut_auto\ai-video-splitter
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:132 -   帧批量大小: 2000
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:133 -   场景批量大小: 500
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:134 -   视频编码预设: p4
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:135 -   视频码率: 5M
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:136 -   处理模式: fast
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:160 - 使用字幕文件: F:\github\aicut_auto\原视频合并\cutbefore.srt
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - _call_ai_splitter_script:186 - 进度: 10% - 准备调用AI分割脚本...
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:206 - 使用subprocess方式调用AI分割脚本...
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:207 - 进度: 20% - 启动AI视频分割进程...
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:226 - 使用Python解释器: F:\github\aicut_auto\.venv\Scripts\python.exe
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:227 - 执行命令: F:\github\aicut_auto\.venv\Scripts\python.exe F:\github\aicut_auto\2-2-2_ai_video_splitter(全视频场景分割).py F:\github\aicut_auto\原视频合并\cutbefore.mp4 F:\github\aicut_auto\原视频合并\cutbefore.srt F:\github\aicut_auto\ai-video-splitter --frame-batch 2000 --scene-batch 500 --encode-preset p4 --bitrate 5M
2025-08-15 19:53:51 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:248 - 已注册AI分割子进程PID: 26432
2025-08-15 19:59:42 - aicut_auto - INFO - shutdown_event:825 - AI Cut Auto Backend 关闭中...
2025-08-15 19:59:42 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:345 - AI分割任务被用户主动停止
2025-08-15 19:59:42 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:346 - 进度: 停止 - AI视频分割已被用户取消
2025-08-15 19:59:42 - aicut_auto - INFO - shutdown_event:837 - AI Cut Auto Backend 已关闭
2025-08-15 19:59:42 - aicut_auto.core.ai_video_splitter - INFO - _count_generated_files:404 - 生成了 585 个场景文件
2025-08-15 20:18:47 - aicut_auto - INFO - _log_startup:101 - 日志系统已初始化
2025-08-15 20:18:47 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-15 20:18:47 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-15 20:18:47 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-15 20:18:47 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-15 20:18:47 - aicut_auto - INFO - _log_startup:101 - 日志系统已初始化
2025-08-15 20:18:47 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-15 20:18:47 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-15 20:18:47 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-15 20:18:47 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-15 20:18:47 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-15 20:18:47 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-15 20:18:47 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-15 20:18:47 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-15 20:18:47 - aicut_auto - INFO - startup_event:811 - === AI Cut Auto Backend 启动 ===
2025-08-15 20:18:47 - aicut_auto - INFO - startup_event:812 - 项目根目录: F:\github\aicut_auto
2025-08-15 20:18:47 - aicut_auto - INFO - startup_event:814 - 任务管理器已初始化
2025-08-15 20:18:47 - aicut_auto - INFO - startup_event:818 - 全局进程跟踪器已初始化
2025-08-15 20:18:47 - aicut_auto - INFO - startup_event:820 - === 后端服务启动完成 ===
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - __init__:36 - AI视频分割器已启用进程跟踪，task_id: 21b00ee4-2033-49f7-8887-2ff4eb96be1e
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - split_video:61 - 开始AI视频分割任务，参数: {'inputVideoPath': 'F:\\github\\aicut_auto\\原视频合并\\cutbefore.mp4', 'outputDirPath': 'F:\\github\\aicut_auto\\ai-video-splitter', 'frameBatchSize': 2000, 'sceneBatchSize': 500, 'videoEncodePreset': 'p4', 'videoBitrate': '5M', 'processMode': 'fast'}
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:129 - AI视频分割开始:
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:130 -   输入视频: F:\github\aicut_auto\原视频合并\cutbefore.mp4
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:131 -   输出目录: F:\github\aicut_auto\ai-video-splitter
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:132 -   帧批量大小: 2000
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:133 -   场景批量大小: 500
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:134 -   视频编码预设: p4
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:135 -   视频码率: 5M
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:136 -   处理模式: fast
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:160 - 使用字幕文件: F:\github\aicut_auto\原视频合并\cutbefore.srt
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - _call_ai_splitter_script:186 - 进度: 10% - 准备调用AI分割脚本...
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:206 - 使用subprocess方式调用AI分割脚本...
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:207 - 进度: 20% - 启动AI视频分割进程...
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:226 - 使用Python解释器: F:\github\aicut_auto\.venv\Scripts\python.exe
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:227 - 执行命令: F:\github\aicut_auto\.venv\Scripts\python.exe F:\github\aicut_auto\2-2-2_ai_video_splitter(全视频场景分割).py F:\github\aicut_auto\原视频合并\cutbefore.mp4 F:\github\aicut_auto\原视频合并\cutbefore.srt F:\github\aicut_auto\ai-video-splitter --frame-batch 2000 --scene-batch 500 --encode-preset p4 --bitrate 5M
2025-08-15 20:20:53 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:248 - 已注册AI分割子进程PID: 79692
2025-08-15 20:23:36 - aicut_auto - INFO - shutdown_event:825 - AI Cut Auto Backend 关闭中...
2025-08-15 20:23:36 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:345 - AI分割任务被用户主动停止
2025-08-15 20:23:36 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:346 - 进度: 停止 - AI视频分割已被用户取消
2025-08-15 20:23:36 - aicut_auto - INFO - shutdown_event:837 - AI Cut Auto Backend 已关闭
2025-08-15 20:23:36 - aicut_auto.core.ai_video_splitter - INFO - _count_generated_files:404 - 生成了 585 个场景文件
2025-08-15 20:23:42 - aicut_auto - INFO - _log_startup:101 - 日志系统已初始化
2025-08-15 20:23:42 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-15 20:23:42 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-15 20:23:42 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-15 20:23:42 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-15 20:23:42 - aicut_auto - INFO - _log_startup:101 - 日志系统已初始化
2025-08-15 20:23:43 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-15 20:23:43 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-15 20:23:43 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-15 20:23:43 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-15 20:23:43 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-15 20:23:43 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-15 20:23:43 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-15 20:23:43 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-15 20:23:43 - aicut_auto - INFO - startup_event:811 - === AI Cut Auto Backend 启动 ===
2025-08-15 20:23:43 - aicut_auto - INFO - startup_event:812 - 项目根目录: F:\github\aicut_auto
2025-08-15 20:23:43 - aicut_auto - INFO - startup_event:814 - 任务管理器已初始化
2025-08-15 20:23:43 - aicut_auto - INFO - startup_event:818 - 全局进程跟踪器已初始化
2025-08-15 20:23:43 - aicut_auto - INFO - startup_event:820 - === 后端服务启动完成 ===
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - __init__:36 - AI视频分割器已启用进程跟踪，task_id: d030fcb6-1e11-4efc-ad3a-a3856b109663
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - split_video:61 - 开始AI视频分割任务，参数: {'inputVideoPath': 'F:\\github\\aicut_auto\\原视频合并\\cutbefore.mp4', 'outputDirPath': 'F:\\github\\aicut_auto\\ai-video-splitter', 'frameBatchSize': 2000, 'sceneBatchSize': 500, 'videoEncodePreset': 'p4', 'videoBitrate': '5M', 'processMode': 'fast'}
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:129 - AI视频分割开始:
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:130 -   输入视频: F:\github\aicut_auto\原视频合并\cutbefore.mp4
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:131 -   输出目录: F:\github\aicut_auto\ai-video-splitter
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:132 -   帧批量大小: 2000
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:133 -   场景批量大小: 500
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:134 -   视频编码预设: p4
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:135 -   视频码率: 5M
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:136 -   处理模式: fast
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:160 - 使用字幕文件: F:\github\aicut_auto\原视频合并\cutbefore.srt
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - _call_ai_splitter_script:186 - 进度: 10% - 准备调用AI分割脚本...
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:206 - 使用subprocess方式调用AI分割脚本...
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:207 - 进度: 20% - 启动AI视频分割进程...
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:226 - 使用Python解释器: F:\github\aicut_auto\.venv\Scripts\python.exe
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:227 - 执行命令: F:\github\aicut_auto\.venv\Scripts\python.exe F:\github\aicut_auto\2-2-2_ai_video_splitter(全视频场景分割).py F:\github\aicut_auto\原视频合并\cutbefore.mp4 F:\github\aicut_auto\原视频合并\cutbefore.srt F:\github\aicut_auto\ai-video-splitter --frame-batch 2000 --scene-batch 500 --encode-preset p4 --bitrate 5M
2025-08-15 20:23:53 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:248 - 已注册AI分割子进程PID: 78884
2025-08-15 20:29:22 - aicut_auto - INFO - shutdown_event:825 - AI Cut Auto Backend 关闭中...
2025-08-15 20:29:22 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:345 - AI分割任务被用户主动停止
2025-08-15 20:29:22 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:346 - 进度: 停止 - AI视频分割已被用户取消
2025-08-15 20:29:22 - aicut_auto - INFO - shutdown_event:837 - AI Cut Auto Backend 已关闭
2025-08-15 20:29:22 - aicut_auto.core.ai_video_splitter - INFO - _count_generated_files:404 - 生成了 0 个场景文件
2025-08-15 20:33:56 - aicut_auto - INFO - _log_startup:101 - 日志系统已初始化
2025-08-15 20:33:56 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-15 20:33:56 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-15 20:33:56 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-15 20:33:56 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-15 20:33:56 - aicut_auto - INFO - _log_startup:101 - 日志系统已初始化
2025-08-15 20:33:56 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-15 20:33:56 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-15 20:33:56 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-15 20:33:56 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-15 20:33:56 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-15 20:33:56 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-15 20:33:56 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-15 20:33:56 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-15 20:33:56 - aicut_auto - INFO - startup_event:811 - === AI Cut Auto Backend 启动 ===
2025-08-15 20:33:56 - aicut_auto - INFO - startup_event:812 - 项目根目录: F:\github\aicut_auto
2025-08-15 20:33:56 - aicut_auto - INFO - startup_event:814 - 任务管理器已初始化
2025-08-15 20:33:56 - aicut_auto - INFO - startup_event:818 - 全局进程跟踪器已初始化
2025-08-15 20:33:56 - aicut_auto - INFO - startup_event:820 - === 后端服务启动完成 ===
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - __init__:36 - AI视频分割器已启用进程跟踪，task_id: c508aab0-8061-4015-8e2e-de4b391e833c
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - split_video:61 - 开始AI视频分割任务，参数: {'inputVideoPath': 'F:\\github\\aicut_auto\\原视频合并\\cutbefore.mp4', 'outputDirPath': 'F:\\github\\aicut_auto\\ai-video-splitter', 'frameBatchSize': 2000, 'sceneBatchSize': 500, 'videoEncodePreset': 'p4', 'videoBitrate': '5M', 'processMode': 'fast'}
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:129 - AI视频分割开始:
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:130 -   输入视频: F:\github\aicut_auto\原视频合并\cutbefore.mp4
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:131 -   输出目录: F:\github\aicut_auto\ai-video-splitter
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:132 -   帧批量大小: 2000
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:133 -   场景批量大小: 500
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:134 -   视频编码预设: p4
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:135 -   视频码率: 5M
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:136 -   处理模式: fast
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:160 - 使用字幕文件: F:\github\aicut_auto\原视频合并\cutbefore.srt
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - _call_ai_splitter_script:186 - 进度: 10% - 准备调用AI分割脚本...
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:206 - 使用subprocess方式调用AI分割脚本...
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:207 - 进度: 20% - 启动AI视频分割进程...
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:226 - 使用Python解释器: F:\github\aicut_auto\.venv\Scripts\python.exe
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:227 - 执行命令: F:\github\aicut_auto\.venv\Scripts\python.exe F:\github\aicut_auto\2-2-2_ai_video_splitter(全视频场景分割).py F:\github\aicut_auto\原视频合并\cutbefore.mp4 F:\github\aicut_auto\原视频合并\cutbefore.srt F:\github\aicut_auto\ai-video-splitter --frame-batch 2000 --scene-batch 500 --encode-preset p4 --bitrate 5M
2025-08-15 20:42:11 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:248 - 已注册AI分割子进程PID: 73620
2025-08-15 20:50:46 - aicut_auto - INFO - shutdown_event:825 - AI Cut Auto Backend 关闭中...
2025-08-15 20:50:46 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:345 - AI分割任务被用户主动停止
2025-08-15 20:50:46 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:346 - 进度: 停止 - AI视频分割已被用户取消
2025-08-15 20:50:46 - aicut_auto - INFO - shutdown_event:837 - AI Cut Auto Backend 已关闭
2025-08-15 20:50:46 - aicut_auto.core.ai_video_splitter - INFO - _count_generated_files:404 - 生成了 0 个场景文件
2025-08-16 08:40:14 - aicut_auto - INFO - _log_startup:101 - 日志系统已初始化
2025-08-16 08:40:14 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-16 08:40:14 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-16 08:40:14 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-16 08:40:14 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-16 08:40:14 - aicut_auto - INFO - _log_startup:101 - 日志系统已初始化
2025-08-16 08:40:15 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-16 08:40:15 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-16 08:40:15 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-16 08:40:15 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-16 08:40:15 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-16 08:40:15 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-16 08:40:15 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-16 08:40:15 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-16 08:40:15 - aicut_auto - INFO - startup_event:811 - === AI Cut Auto Backend 启动 ===
2025-08-16 08:40:15 - aicut_auto - INFO - startup_event:812 - 项目根目录: F:\github\aicut_auto
2025-08-16 08:40:15 - aicut_auto - INFO - startup_event:814 - 任务管理器已初始化
2025-08-16 08:40:15 - aicut_auto - INFO - startup_event:818 - 全局进程跟踪器已初始化
2025-08-16 08:40:15 - aicut_auto - INFO - startup_event:820 - === 后端服务启动完成 ===
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - __init__:36 - AI视频分割器已启用进程跟踪，task_id: 1d1152a6-50c1-4050-8a63-471818e4959c
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - split_video:61 - 开始AI视频分割任务，参数: {'inputVideoPath': 'F:\\github\\aicut_auto\\原视频合并\\cutbefore.mp4', 'outputDirPath': 'F:\\github\\aicut_auto\\ai-video-splitter', 'frameBatchSize': 2000, 'sceneBatchSize': 500, 'videoEncodePreset': 'p4', 'videoBitrate': '5M', 'processMode': 'fast'}
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:129 - AI视频分割开始:
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:130 -   输入视频: F:\github\aicut_auto\原视频合并\cutbefore.mp4
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:131 -   输出目录: F:\github\aicut_auto\ai-video-splitter
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:132 -   帧批量大小: 2000
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:133 -   场景批量大小: 500
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:134 -   视频编码预设: p4
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:135 -   视频码率: 5M
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:136 -   处理模式: fast
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:160 - 使用字幕文件: F:\github\aicut_auto\原视频合并\cutbefore.srt
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - _call_ai_splitter_script:186 - 进度: 10% - 准备调用AI分割脚本...
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:206 - 使用subprocess方式调用AI分割脚本...
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:207 - 进度: 20% - 启动AI视频分割进程...
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:226 - 使用Python解释器: F:\github\aicut_auto\.venv\Scripts\python.exe
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:227 - 执行命令: F:\github\aicut_auto\.venv\Scripts\python.exe F:\github\aicut_auto\2-2-2_ai_video_splitter(全视频场景分割).py F:\github\aicut_auto\原视频合并\cutbefore.mp4 F:\github\aicut_auto\原视频合并\cutbefore.srt F:\github\aicut_auto\ai-video-splitter --frame-batch 2000 --scene-batch 500 --encode-preset p4 --bitrate 5M
2025-08-16 08:40:34 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:248 - 已注册AI分割子进程PID: 81400
2025-08-16 08:47:29 - aicut_auto - INFO - shutdown_event:825 - AI Cut Auto Backend 关闭中...
2025-08-16 08:47:29 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:345 - AI分割任务被用户主动停止
2025-08-16 08:47:29 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:346 - 进度: 停止 - AI视频分割已被用户取消
2025-08-16 08:47:29 - aicut_auto - INFO - shutdown_event:837 - AI Cut Auto Backend 已关闭
2025-08-16 08:47:29 - aicut_auto.core.ai_video_splitter - INFO - _count_generated_files:404 - 生成了 0 个场景文件
2025-08-16 08:47:35 - aicut_auto - INFO - _log_startup:101 - 日志系统已初始化
2025-08-16 08:47:35 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-16 08:47:35 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-16 08:47:35 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-16 08:47:35 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-16 08:47:36 - aicut_auto - INFO - _log_startup:101 - 日志系统已初始化
2025-08-16 08:47:36 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-16 08:47:36 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-16 08:47:36 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-16 08:47:36 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-16 08:47:36 - aicut_auto - INFO - <module>:79 - 音频静态文件服务已启动: /audio -> F:\github\aicut_auto\output
2025-08-16 08:47:36 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media1 -> F:\github\aicut_auto\newcut_ai
2025-08-16 08:47:36 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media2 -> F:\github\aicut_auto\ai-video-splitter
2025-08-16 08:47:36 - aicut_auto - INFO - <module>:94 - 媒体文件服务已启动: /media3 -> F:\github\aicut_auto\原视频合并
2025-08-16 08:47:36 - aicut_auto - INFO - startup_event:811 - === AI Cut Auto Backend 启动 ===
2025-08-16 08:47:36 - aicut_auto - INFO - startup_event:812 - 项目根目录: F:\github\aicut_auto
2025-08-16 08:47:36 - aicut_auto - INFO - startup_event:814 - 任务管理器已初始化
2025-08-16 08:47:36 - aicut_auto - INFO - startup_event:818 - 全局进程跟踪器已初始化
2025-08-16 08:47:36 - aicut_auto - INFO - startup_event:820 - === 后端服务启动完成 ===
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - __init__:36 - AI视频分割器已启用进程跟踪，task_id: 64f8954a-720e-4ec4-bf11-fdfdb6da86f8
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - split_video:61 - 开始AI视频分割任务，参数: {'inputVideoPath': 'F:\\github\\aicut_auto\\原视频合并\\cutbefore.mp4', 'outputDirPath': 'F:\\github\\aicut_auto\\ai-video-splitter', 'frameBatchSize': 2000, 'sceneBatchSize': 500, 'videoEncodePreset': 'p4', 'videoBitrate': '5M', 'processMode': 'fast'}
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:129 - AI视频分割开始:
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:130 -   输入视频: F:\github\aicut_auto\原视频合并\cutbefore.mp4
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:131 -   输出目录: F:\github\aicut_auto\ai-video-splitter
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:132 -   帧批量大小: 2000
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:133 -   场景批量大小: 500
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:134 -   视频编码预设: p4
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:135 -   视频码率: 5M
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:136 -   处理模式: fast
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:160 - 使用字幕文件: F:\github\aicut_auto\原视频合并\cutbefore.srt
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - _call_ai_splitter_script:186 - 进度: 10% - 准备调用AI分割脚本...
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:206 - 使用subprocess方式调用AI分割脚本...
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:207 - 进度: 20% - 启动AI视频分割进程...
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:226 - 使用Python解释器: F:\github\aicut_auto\.venv\Scripts\python.exe
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:227 - 执行命令: F:\github\aicut_auto\.venv\Scripts\python.exe F:\github\aicut_auto\2-2-2_ai_video_splitter(全视频场景分割).py F:\github\aicut_auto\原视频合并\cutbefore.mp4 F:\github\aicut_auto\原视频合并\cutbefore.srt F:\github\aicut_auto\ai-video-splitter --frame-batch 2000 --scene-batch 500 --encode-preset p4 --bitrate 5M
2025-08-16 09:06:33 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:248 - 已注册AI分割子进程PID: 78968
2025-08-16 09:15:50 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:332 - 进度: 100% - AI视频分割完成
2025-08-16 09:15:50 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:333 - AI分割脚本执行成功
2025-08-16 09:15:50 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:336 - 脚本stderr输出:
2025-08-16 09:15:50 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   2025-08-16 09:06:34.584235: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-08-16 09:15:50 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   2025-08-16 09:06:35.184563: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-08-16 09:15:50 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   2025-08-16 09:06:39.555464: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.
2025-08-16 09:15:50 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   To enable the following instructions: SSE3 SSE4.1 SSE4.2 AVX AVX2 AVX_VNNI FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.
2025-08-16 09:15:50 - aicut_auto.core.ai_video_splitter - INFO - _count_generated_files:404 - 生成了 1729 个场景文件
2025-08-16 09:15:50 - aicut_auto.core.ai_video_splitter - INFO - split_video:92 - AI视频分割任务完成
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - __init__:36 - AI视频分割器已启用进程跟踪，task_id: c2bcb9bb-d124-4d44-a2dc-6e842eacc64b
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - split_video:61 - 开始AI视频分割任务，参数: {'inputVideoPath': 'F:\\github\\aicut_auto\\原视频合并\\cutbefore.mp4', 'outputDirPath': 'F:\\github\\aicut_auto\\ai-video-splitter', 'frameBatchSize': 2000, 'sceneBatchSize': 500, 'videoEncodePreset': 'p4', 'videoBitrate': '5M', 'processMode': 'fast'}
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:129 - AI视频分割开始:
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:130 -   输入视频: F:\github\aicut_auto\原视频合并\cutbefore.mp4
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:131 -   输出目录: F:\github\aicut_auto\ai-video-splitter
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:132 -   帧批量大小: 2000
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:133 -   场景批量大小: 500
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:134 -   视频编码预设: p4
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:135 -   视频码率: 5M
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:136 -   处理模式: fast
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - _split_video_sync:160 - 使用字幕文件: F:\github\aicut_auto\原视频合并\cutbefore.srt
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - _call_ai_splitter_script:186 - 进度: 10% - 准备调用AI分割脚本...
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:206 - 使用subprocess方式调用AI分割脚本...
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:207 - 进度: 20% - 启动AI视频分割进程...
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:226 - 使用Python解释器: F:\github\aicut_auto\.venv\Scripts\python.exe
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:227 - 执行命令: F:\github\aicut_auto\.venv\Scripts\python.exe F:\github\aicut_auto\2-2-2_ai_video_splitter(全视频场景分割).py F:\github\aicut_auto\原视频合并\cutbefore.mp4 F:\github\aicut_auto\原视频合并\cutbefore.srt F:\github\aicut_auto\ai-video-splitter --frame-batch 2000 --scene-batch 500 --encode-preset p4 --bitrate 5M
2025-08-16 10:22:17 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:248 - 已注册AI分割子进程PID: 80780
2025-08-16 10:32:23 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:332 - 进度: 100% - AI视频分割完成
2025-08-16 10:32:23 - aicut_auto.core.ai_video_splitter - INFO - _call_splitter_subprocess:333 - AI分割脚本执行成功
2025-08-16 10:32:23 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:336 - 脚本stderr输出:
2025-08-16 10:32:23 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   2025-08-16 10:22:19.242723: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-08-16 10:32:23 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   2025-08-16 10:22:19.846816: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-08-16 10:32:23 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   2025-08-16 10:22:24.413507: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.
2025-08-16 10:32:23 - aicut_auto.core.ai_video_splitter - WARNING - _call_splitter_subprocess:338 -   To enable the following instructions: SSE3 SSE4.1 SSE4.2 AVX AVX2 AVX_VNNI FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.
2025-08-16 10:32:23 - aicut_auto.core.ai_video_splitter - INFO - _count_generated_files:404 - 生成了 1729 个场景文件
2025-08-16 10:32:23 - aicut_auto.core.ai_video_splitter - INFO - split_video:92 - AI视频分割任务完成
